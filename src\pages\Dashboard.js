import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Users,
  Database,
  Activity,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3,
} from "lucide-react";
import DashboardLayout from "../components/layout/DashboardLayout";
import { useAuth } from "../contexts/AuthContext";
import { dataAPI, userAPI, auditAPI } from "../utils/api";

const StatCard = ({ title, value, icon: Icon, color, trend }) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    className="bg-white rounded-xl shadow-sm p-6 border border-gray-200"
  >
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-3xl font-bold text-gray-900 mt-2">{value}</p>
        {trend && (
          <div className="flex items-center mt-2">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600">{trend}</span>
          </div>
        )}
      </div>
      <div className={`p-3 rounded-lg ${color}`}>
        <Icon className="w-6 h-6 text-white" />
      </div>
    </div>
  </motion.div>
);

const ActivityItem = ({ action, user, time, status }) => (
  <div className="flex items-center space-x-3 py-3">
    <div
      className={`w-2 h-2 rounded-full ${
        status === "success"
          ? "bg-green-500"
          : status === "warning"
          ? "bg-yellow-500"
          : "bg-red-500"
      }`}
    />
    <div className="flex-1 min-w-0">
      <p className="text-sm text-gray-900">{action}</p>
      <p className="text-xs text-gray-500">by {user}</p>
    </div>
    <div className="flex items-center text-xs text-gray-500">
      <Clock className="w-3 h-3 mr-1" />
      {time}
    </div>
  </div>
);

const Dashboard = () => {
  const { user, isAdmin } = useAuth();
  const [stats, setStats] = useState([
    {
      title: "Total Users",
      value: "0",
      icon: Users,
      color: "bg-blue-500",
      trend: "Loading...",
    },
    {
      title: "Employeurs",
      value: "0",
      icon: Activity,
      color: "bg-green-500",
      trend: "Loading...",
    },
    {
      title: "Database Records",
      value: "0",
      icon: Database,
      color: "bg-purple-500",
      trend: "Loading...",
    },
    {
      title: "System Health",
      value: "0",
      icon: CheckCircle,
      color: "bg-emerald-500",
      trend: "Loading...",
    },
  ]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load data counts
      const [employeursRes, assuresRes, beneficiairesRes, usersRes] =
        await Promise.all([
          dataAPI.getEmployeurs().catch(() => ({ employeurs: [], total: 0 })),
          dataAPI.getAssures().catch(() => ({ assures: [], total: 0 })),
          dataAPI
            .getBeneficiaires()
            .catch(() => ({ beneficiaires: [], total: 0 })),
          isAdmin()
            ? userAPI.getUsers().catch(() => ({ users: [] }))
            : Promise.resolve({ users: [] }),
        ]);

      const totalRecords =
        (employeursRes.total || employeursRes.employeurs?.length || 0) +
        (assuresRes.total || assuresRes.assures?.length || 0) +
        (beneficiairesRes.total || beneficiairesRes.beneficiaires?.length || 0);

      setStats([
        {
          title: "Total Users",
          value: usersRes.users?.length || 0,
          icon: Users,
          color: "bg-blue-500",
          trend: "System users",
        },
        {
          title: "Employeurs",
          value: employeursRes.total || employeursRes.employeurs?.length || 0,
          icon: Activity,
          color: "bg-green-500",
          trend: "Registered companies",
        },
        {
          title: "Database Records",
          value: totalRecords,
          icon: Database,
          color: "bg-purple-500",
          trend: "Total records",
        },
        {
          title: "System Health",
          value: "100%",
          icon: CheckCircle,
          color: "bg-emerald-500",
          trend: "All systems operational",
        },
      ]);
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const recentActivity = [
    {
      action: "New user registered",
      user: "admin",
      time: "2 minutes ago",
      status: "success",
    },
    {
      action: "Database backup completed",
      user: "system",
      time: "15 minutes ago",
      status: "success",
    },
    {
      action: "API rate limit exceeded",
      user: "agent_user",
      time: "1 hour ago",
      status: "warning",
    },
    {
      action: "User login failed",
      user: "unknown",
      time: "2 hours ago",
      status: "error",
    },
  ];

  return (
    <DashboardLayout currentPage="dashboard">
      <div className="space-y-6">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-6 text-white"
        >
          <h1 className="text-2xl font-bold mb-2">
            Welcome back, {user?.username}!
          </h1>
          <p className="text-blue-100">
            Here's what's happening with your CNSS system today.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <StatCard {...stat} />
            </motion.div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200"
          >
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Recent Activity
                </h3>
                <Activity className="w-5 h-5 text-gray-400" />
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-1">
                {recentActivity.map((activity, index) => (
                  <ActivityItem key={index} {...activity} />
                ))}
              </div>
              <button className="w-full mt-4 text-sm text-blue-600 hover:text-blue-700 font-medium">
                View all activity
              </button>
            </div>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200"
          >
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Quick Actions
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                <button className="w-full flex items-center justify-between p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3">
                    <Database className="w-5 h-5 text-blue-500" />
                    <span className="font-medium">Manage Data</span>
                  </div>
                  <span className="text-gray-400">→</span>
                </button>

                {isAdmin() && (
                  <>
                    <button className="w-full flex items-center justify-between p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-3">
                        <Users className="w-5 h-5 text-green-500" />
                        <span className="font-medium">User Management</span>
                      </div>
                      <span className="text-gray-400">→</span>
                    </button>

                    <button className="w-full flex items-center justify-between p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-3">
                        <BarChart3 className="w-5 h-5 text-purple-500" />
                        <span className="font-medium">View Reports</span>
                      </div>
                      <span className="text-gray-400">→</span>
                    </button>
                  </>
                )}

                <button className="w-full flex items-center justify-between p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3">
                    <AlertCircle className="w-5 h-5 text-orange-500" />
                    <span className="font-medium">System Status</span>
                  </div>
                  <span className="text-gray-400">→</span>
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
