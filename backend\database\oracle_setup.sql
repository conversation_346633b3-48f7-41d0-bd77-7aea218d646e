-- Oracle Database Setup Script for CNSS Application
-- Run this script as cnss_user/cnss_password@XEPDB1

-- Create sequences for auto-increment IDs
CREATE SEQUENCE employeur_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE assure_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE beneficiaire_seq START WITH 1 INCREMENT BY 1;

-- Create Employeurs table
CREATE TABLE employeurs (
    id NUMBER PRIMARY KEY,
    nom_entreprise VARCHAR2(255) NOT NULL,
    numero_cnss VARCHAR2(50) UNIQUE NOT NULL,
    adresse CLOB,
    ville VARCHAR2(100),
    telephone VARCHAR2(20),
    email VARCHAR2(255),
    secteur_activite VARCHAR2(255),
    statut VARCHAR2(20) DEFAULT 'Actif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create Assures table
CREATE TABLE assures (
    id NUMBER PRIMARY KEY,
    nom VARCHAR2(100) NOT NULL,
    prenom VARCHAR2(100) NOT NULL,
    cin VARCHAR2(20) UNIQUE NOT NULL,
    numero_assure VARCHAR2(50) UNIQUE NOT NULL,
    date_naissance DATE,
    lieu_naissance VARCHAR2(255),
    adresse CLOB,
    telephone VARCHAR2(20),
    email VARCHAR2(255),
    employeur_id NUMBER,
    statut VARCHAR2(20) DEFAULT 'Actif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_assure_employeur FOREIGN KEY (employeur_id) REFERENCES employeurs(id)
);

-- Create Beneficiaires table
CREATE TABLE beneficiaires (
    id NUMBER PRIMARY KEY,
    nom VARCHAR2(100) NOT NULL,
    prenom VARCHAR2(100) NOT NULL,
    cin VARCHAR2(20),
    date_naissance DATE,
    relation_assure VARCHAR2(50) NOT NULL,
    assure_id NUMBER NOT NULL,
    numero_beneficiaire VARCHAR2(50),
    statut VARCHAR2(20) DEFAULT 'Actif',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_beneficiaire_assure FOREIGN KEY (assure_id) REFERENCES assures(id)
);

-- Create triggers for auto-increment
CREATE OR REPLACE TRIGGER employeur_trigger
BEFORE INSERT ON employeurs
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        SELECT employeur_seq.NEXTVAL INTO :NEW.id FROM DUAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER assure_trigger
BEFORE INSERT ON assures
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        SELECT assure_seq.NEXTVAL INTO :NEW.id FROM DUAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER beneficiaire_trigger
BEFORE INSERT ON beneficiaires
FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        SELECT beneficiaire_seq.NEXTVAL INTO :NEW.id FROM DUAL;
    END IF;
END;
/

-- Create triggers for updated_at timestamp
CREATE OR REPLACE TRIGGER employeur_updated_trigger
BEFORE UPDATE ON employeurs
FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER assure_updated_trigger
BEFORE UPDATE ON assures
FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

CREATE OR REPLACE TRIGGER beneficiaire_updated_trigger
BEFORE UPDATE ON beneficiaires
FOR EACH ROW
BEGIN
    :NEW.updated_at := CURRENT_TIMESTAMP;
END;
/

-- Insert sample data
INSERT INTO employeurs (nom_entreprise, numero_cnss, adresse, ville, telephone, email, secteur_activite, statut)
VALUES ('CNSS Maroc', 'EMP001', '123 Rue Mohammed V, Casablanca', 'Casablanca', '+212522123456', '<EMAIL>', 'Administration Publique', 'Actif');

INSERT INTO employeurs (nom_entreprise, numero_cnss, adresse, ville, telephone, email, secteur_activite, statut)
VALUES ('TechCorp Solutions', 'EMP002', '456 Avenue Hassan II, Rabat', 'Rabat', '+212537654321', '<EMAIL>', 'Technologies', 'Actif');

INSERT INTO employeurs (nom_entreprise, numero_cnss, adresse, ville, telephone, email, secteur_activite, statut)
VALUES ('Atlas Industries', 'EMP003', '789 Boulevard Zerktouni, Casablanca', 'Casablanca', '+212522987654', '<EMAIL>', 'Industrie', 'Actif');

-- Insert sample assures
INSERT INTO assures (nom, prenom, cin, numero_assure, date_naissance, lieu_naissance, adresse, telephone, email, employeur_id, statut)
VALUES ('Alami', 'Ahmed', 'AB123456', 'ASS001', DATE '1985-03-15', 'Casablanca', '123 Rue de la Paix, Casablanca', '+212661234567', '<EMAIL>', 1, 'Actif');

INSERT INTO assures (nom, prenom, cin, numero_assure, date_naissance, lieu_naissance, adresse, telephone, email, employeur_id, statut)
VALUES ('Benali', 'Fatima', 'CD789012', 'ASS002', DATE '1990-07-22', 'Rabat', '456 Avenue Mohammed VI, Rabat', '+212662345678', '<EMAIL>', 2, 'Actif');

INSERT INTO assures (nom, prenom, cin, numero_assure, date_naissance, lieu_naissance, adresse, telephone, email, employeur_id, statut)
VALUES ('Chakir', 'Omar', 'EF345678', 'ASS003', DATE '1988-11-10', 'Fes', '789 Rue Atlas, Fes', '+212663456789', '<EMAIL>', 3, 'Actif');

-- Insert sample beneficiaires
INSERT INTO beneficiaires (nom, prenom, cin, date_naissance, relation_assure, assure_id, numero_beneficiaire, statut)
VALUES ('Alami', 'Aicha', 'GH901234', DATE '1987-05-20', 'Épouse', 1, 'BEN001', 'Actif');

INSERT INTO beneficiaires (nom, prenom, cin, date_naissance, relation_assure, assure_id, numero_beneficiaire, statut)
VALUES ('Alami', 'Youssef', 'IJ567890', DATE '2010-12-03', 'Enfant', 1, 'BEN002', 'Actif');

INSERT INTO beneficiaires (nom, prenom, cin, date_naissance, relation_assure, assure_id, numero_beneficiaire, statut)
VALUES ('Benali', 'Karim', 'KL123456', DATE '2015-08-14', 'Enfant', 2, 'BEN003', 'Actif');

-- Create indexes for better performance
CREATE INDEX idx_employeur_numero_cnss ON employeurs(numero_cnss);
CREATE INDEX idx_employeur_nom ON employeurs(nom_entreprise);
CREATE INDEX idx_assure_cin ON assures(cin);
CREATE INDEX idx_assure_numero ON assures(numero_assure);
CREATE INDEX idx_assure_employeur ON assures(employeur_id);
CREATE INDEX idx_beneficiaire_assure ON beneficiaires(assure_id);

-- Commit all changes
COMMIT;

-- Display success message
SELECT 'Oracle database setup completed successfully!' AS status FROM DUAL;
