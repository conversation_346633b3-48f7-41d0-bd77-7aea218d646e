import os
from datetime import timedelta
from dotenv import load_dotenv
from urllib.parse import quote_plus

load_dotenv()

class Config:
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-this')
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt-secret-string-change-this')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)

    # Database type selection
    DB_TYPE = os.getenv('DB_TYPE', 'sqlite')  # 'sqlite' or 'oracle'

    # SQLite configuration (default for development)
    SQLITE_DATABASE_URI = os.getenv('DATABASE_URL', 'sqlite:///cnss_users.db')

    # Oracle configuration
    ORACLE_HOST = os.getenv('ORACLE_HOST', 'localhost')
    ORACLE_PORT = os.getenv('ORACLE_PORT', '1521')
    ORACLE_SERVICE_NAME = os.getenv('ORACLE_SERVICE_NAME', 'XE')
    ORACLE_USERNAME = os.getenv('ORACLE_USERNAME', 'cnss_user')
    ORACLE_PASSWORD = os.getenv('ORACLE_PASSWORD', 'password')

    # Set database URI based on type
    if DB_TYPE.lower() == 'oracle':
        password_encoded = quote_plus(ORACLE_PASSWORD)
        SQLALCHEMY_DATABASE_URI = f"oracle+oracledb://{ORACLE_USERNAME}:{password_encoded}@{ORACLE_HOST}:{ORACLE_PORT}/?service_name={ORACLE_SERVICE_NAME}"
    else:
        SQLALCHEMY_DATABASE_URI = SQLITE_DATABASE_URI

    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = os.getenv('SQLALCHEMY_ECHO', 'False').lower() == 'true'

    # Legacy Oracle configuration for backward compatibility
    ORACLE_USER = os.getenv('ORACLE_USER', ORACLE_USERNAME)
    ORACLE_DSN = f"{ORACLE_HOST}:{ORACLE_PORT}/{ORACLE_SERVICE_NAME}"

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """Get configuration based on environment"""
    env = os.getenv('FLASK_ENV', 'development')
    return config.get(env, config['default'])
