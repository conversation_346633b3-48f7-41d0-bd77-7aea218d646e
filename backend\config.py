import os
from datetime import timedelta
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-this')
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt-secret-string-change-this')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    
    # SQLite for user management
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', 'sqlite:///cnss_users.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Oracle configuration for business data
    ORACLE_USER = os.getenv('ORACLE_USER', 'your_oracle_user')
    ORACLE_PASSWORD = os.getenv('ORACLE_PASSWORD', 'your_oracle_password')
    ORACLE_DSN = os.getenv('ORACLE_DSN', 'localhost:1521/XE')

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
