import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import Login from "./components/Login";
import Dashboard from "./pages/Dashboard";
import UserManagement from "./pages/UserManagement";
import "./App.css";

function AppContent() {
  const { isAuthenticated, login } = useAuth();

  if (!isAuthenticated()) {
    return <Login onLogin={login} />;
  }

  return (
    <Router>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/users" element={<UserManagement />} />
        <Route
          path="/data"
          element={<div>Data Management - Coming Soon</div>}
        />
        <Route path="/logs" element={<div>Audit Logs - Coming Soon</div>} />
        <Route path="/settings" element={<div>Settings - Coming Soon</div>} />
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Router>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
