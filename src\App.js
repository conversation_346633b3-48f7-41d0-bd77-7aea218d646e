import React from "react";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import Login from "./components/Login";
import "./App.css";

function AppContent() {
  const { user, isAuthenticated, login, logout } = useAuth();

  if (!isAuthenticated()) {
    return <Login onLogin={login} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Temporary dashboard placeholder */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                CNSS Dashboard
              </h1>
              <p className="text-gray-600">
                Welcome, {user?.username} ({user?.role})
              </p>
            </div>
            <button
              onClick={logout}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Dashboard Content</h2>
          <p className="text-gray-600">
            This is a placeholder for the main dashboard. The full dashboard
            will be implemented in the next phase.
          </p>
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-900">User Information:</h3>
            <pre className="mt-2 text-sm text-blue-800">
              {JSON.stringify(user, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
