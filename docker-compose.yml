version: '3.8'

services:
  # Frontend service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    environment:
      - REACT_APP_API_URL=http://localhost:5000
    networks:
      - cnss-network
    restart: unless-stopped

  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - JWT_SECRET_KEY=jwt-secret-string-change-this-in-production
      - DATABASE_URL=sqlite:///data/cnss_users.db
      - ORACLE_USER=${ORACLE_USER:-your_oracle_user}
      - OR<PERSON><PERSON>_PASSWORD=${ORACLE_PASSWORD:-your_oracle_password}
      - ORACLE_DSN=${ORACLE_DSN:-oracle:1521/XE}
    volumes:
      - backend_data:/app/data
    depends_on:
      - oracle
    networks:
      - cnss-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Oracle Database service (for production)
  oracle:
    image: container-registry.oracle.com/database/express:21.3.0-xe
    ports:
      - "1521:1521"
      - "5500:5500"
    environment:
      - ORACLE_PWD=${ORACLE_PWD:-OraclePassword123}
      - ORACLE_CHARACTERSET=AL32UTF8
    volumes:
      - oracle_data:/opt/oracle/oradata
      - ./oracle-init:/opt/oracle/scripts/startup
    networks:
      - cnss-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sqlplus", "-L", "sys/OraclePassword123@localhost:1521/XE as sysdba", "@/opt/oracle/scripts/healthcheck.sql"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cnss-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - cnss-network
    restart: unless-stopped

volumes:
  backend_data:
    driver: local
  oracle_data:
    driver: local
  redis_data:
    driver: local

networks:
  cnss-network:
    driver: bridge
