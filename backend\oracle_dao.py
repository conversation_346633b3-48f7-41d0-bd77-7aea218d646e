"""
Oracle Data Access Object (DAO) for business data operations
"""
import logging
from datetime import datetime
from flask import current_app
from database import db_manager

logger = logging.getLogger(__name__)

class OracleDAO:
    """Data Access Object for Oracle database operations"""
    
    @staticmethod
    def get_connection():
        """Get Oracle database connection"""
        return db_manager.get_oracle_connection()
    
    @staticmethod
    def execute_query(query, params=None, fetch_one=False, fetch_all=False):
        """Execute a query and return results"""
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params or {})
                
                if fetch_one:
                    result = cursor.fetchone()
                    cursor.close()
                    return result
                elif fetch_all:
                    result = cursor.fetchall()
                    cursor.close()
                    return result
                else:
                    connection.commit()
                    cursor.close()
                    return True
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            raise
    
    @staticmethod
    def dict_from_row(cursor, row):
        """Convert Oracle row to dictionary"""
        if not row:
            return None
        columns = [col[0].lower() for col in cursor.description]
        return dict(zip(columns, row))

class EmployeurDAO:
    """Data Access Object for Employeur operations"""
    
    @staticmethod
    def get_all(search=None, limit=None, offset=None):
        """Get all employeurs with optional search and pagination"""
        query = """
        SELECT id, nom_entreprise, numero_cnss, adresse, ville, 
               telephone, email, secteur_activite, statut, 
               created_at, updated_at
        FROM employeurs
        """
        params = {}
        
        if search:
            query += " WHERE UPPER(nom_entreprise) LIKE UPPER(:search) OR UPPER(numero_cnss) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"
        
        query += " ORDER BY created_at DESC"
        
        if limit:
            if offset:
                query += " OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY"
                params['offset'] = offset
            else:
                query += " FETCH FIRST :limit ROWS ONLY"
            params['limit'] = limit
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                result = []
                for row in rows:
                    employeur = OracleDAO.dict_from_row(cursor, row)
                    # Convert dates to ISO format
                    if employeur.get('created_at'):
                        employeur['created_at'] = employeur['created_at'].isoformat()
                    if employeur.get('updated_at'):
                        employeur['updated_at'] = employeur['updated_at'].isoformat()
                    result.append(employeur)
                
                cursor.close()
                return result
        except Exception as e:
            logger.error(f"Failed to get employeurs: {e}")
            raise
    
    @staticmethod
    def get_by_id(employeur_id):
        """Get employeur by ID"""
        query = """
        SELECT id, nom_entreprise, numero_cnss, adresse, ville, 
               telephone, email, secteur_activite, statut, 
               created_at, updated_at
        FROM employeurs WHERE id = :id
        """
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, {'id': employeur_id})
                row = cursor.fetchone()
                
                if row:
                    employeur = OracleDAO.dict_from_row(cursor, row)
                    # Convert dates to ISO format
                    if employeur.get('created_at'):
                        employeur['created_at'] = employeur['created_at'].isoformat()
                    if employeur.get('updated_at'):
                        employeur['updated_at'] = employeur['updated_at'].isoformat()
                    cursor.close()
                    return employeur
                
                cursor.close()
                return None
        except Exception as e:
            logger.error(f"Failed to get employeur {employeur_id}: {e}")
            raise
    
    @staticmethod
    def create(data):
        """Create new employeur"""
        query = """
        INSERT INTO employeurs (nom_entreprise, numero_cnss, adresse, ville, 
                               telephone, email, secteur_activite, statut)
        VALUES (:nom_entreprise, :numero_cnss, :adresse, :ville, 
                :telephone, :email, :secteur_activite, :statut)
        """
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, data)
                connection.commit()
                
                # Get the generated ID
                cursor.execute("SELECT employeur_seq.CURRVAL FROM DUAL")
                new_id = cursor.fetchone()[0]
                cursor.close()
                
                return new_id
        except Exception as e:
            logger.error(f"Failed to create employeur: {e}")
            raise
    
    @staticmethod
    def update(employeur_id, data):
        """Update employeur"""
        query = """
        UPDATE employeurs 
        SET nom_entreprise = :nom_entreprise, numero_cnss = :numero_cnss,
            adresse = :adresse, ville = :ville, telephone = :telephone,
            email = :email, secteur_activite = :secteur_activite,
            statut = :statut, updated_at = CURRENT_TIMESTAMP
        WHERE id = :id
        """
        
        data['id'] = employeur_id
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, data)
                connection.commit()
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"Failed to update employeur {employeur_id}: {e}")
            raise
    
    @staticmethod
    def delete(employeur_id):
        """Delete employeur"""
        query = "DELETE FROM employeurs WHERE id = :id"
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, {'id': employeur_id})
                connection.commit()
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"Failed to delete employeur {employeur_id}: {e}")
            raise
    
    @staticmethod
    def count(search=None):
        """Count total employeurs"""
        query = "SELECT COUNT(*) FROM employeurs"
        params = {}
        
        if search:
            query += " WHERE UPPER(nom_entreprise) LIKE UPPER(:search) OR UPPER(numero_cnss) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                count = cursor.fetchone()[0]
                cursor.close()
                return count
        except Exception as e:
            logger.error(f"Failed to count employeurs: {e}")
            raise

class AssureDAO:
    """Data Access Object for Assure operations"""
    
    @staticmethod
    def get_all(search=None, limit=None, offset=None):
        """Get all assures with optional search and pagination"""
        query = """
        SELECT a.id, a.nom, a.prenom, a.cin, a.numero_assure, 
               a.date_naissance, a.lieu_naissance, a.adresse, 
               a.telephone, a.email, a.employeur_id, a.statut,
               a.created_at, a.updated_at, e.nom_entreprise
        FROM assures a
        LEFT JOIN employeurs e ON a.employeur_id = e.id
        """
        params = {}
        
        if search:
            query += " WHERE UPPER(a.nom) LIKE UPPER(:search) OR UPPER(a.prenom) LIKE UPPER(:search) OR UPPER(a.cin) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"
        
        query += " ORDER BY a.created_at DESC"
        
        if limit:
            if offset:
                query += " OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY"
                params['offset'] = offset
            else:
                query += " FETCH FIRST :limit ROWS ONLY"
            params['limit'] = limit
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                result = []
                for row in rows:
                    assure = OracleDAO.dict_from_row(cursor, row)
                    # Convert dates to ISO format
                    if assure.get('date_naissance'):
                        assure['date_naissance'] = assure['date_naissance'].isoformat()
                    if assure.get('created_at'):
                        assure['created_at'] = assure['created_at'].isoformat()
                    if assure.get('updated_at'):
                        assure['updated_at'] = assure['updated_at'].isoformat()
                    result.append(assure)
                
                cursor.close()
                return result
        except Exception as e:
            logger.error(f"Failed to get assures: {e}")
            raise
    
    @staticmethod
    def create(data):
        """Create new assure"""
        query = """
        INSERT INTO assures (nom, prenom, cin, numero_assure, date_naissance,
                            lieu_naissance, adresse, telephone, email, 
                            employeur_id, statut)
        VALUES (:nom, :prenom, :cin, :numero_assure, :date_naissance,
                :lieu_naissance, :adresse, :telephone, :email,
                :employeur_id, :statut)
        """
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, data)
                connection.commit()
                
                # Get the generated ID
                cursor.execute("SELECT assure_seq.CURRVAL FROM DUAL")
                new_id = cursor.fetchone()[0]
                cursor.close()
                
                return new_id
        except Exception as e:
            logger.error(f"Failed to create assure: {e}")
            raise
    
    @staticmethod
    def count(search=None):
        """Count total assures"""
        query = "SELECT COUNT(*) FROM assures"
        params = {}
        
        if search:
            query += " WHERE UPPER(nom) LIKE UPPER(:search) OR UPPER(prenom) LIKE UPPER(:search) OR UPPER(cin) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                count = cursor.fetchone()[0]
                cursor.close()
                return count
        except Exception as e:
            logger.error(f"Failed to count assures: {e}")
            raise

# Similar classes for BeneficiaireDAO would follow the same pattern...
