"""
Oracle Data Access Object (DAO) for business data operations
"""
import logging
from datetime import datetime
from flask import current_app
from database import db_manager

logger = logging.getLogger(__name__)

class OracleDAO:
    """Data Access Object for Oracle database operations"""
    
    @staticmethod
    def get_connection():
        """Get Oracle database connection"""
        return db_manager.get_oracle_connection()
    
    @staticmethod
    def execute_query(query, params=None, fetch_one=False, fetch_all=False):
        """Execute a query and return results"""
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params or {})
                
                if fetch_one:
                    result = cursor.fetchone()
                    cursor.close()
                    return result
                elif fetch_all:
                    result = cursor.fetchall()
                    cursor.close()
                    return result
                else:
                    connection.commit()
                    cursor.close()
                    return True
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            raise
    
    @staticmethod
    def dict_from_row(cursor, row):
        """Convert Oracle row to dictionary"""
        if not row:
            return None
        columns = [col[0].lower() for col in cursor.description]
        return dict(zip(columns, row))

class EmployeurDAO:
    """Data Access Object for Employeur operations"""

    @staticmethod
    def get_all(search=None, limit=None, offset=None):
        """Get all employeurs with optional search and pagination"""
        query = """
        SELECT EMP_MAT, EMP_CLE, EMP_RAIS, EMP_SIGLE, EMP_ACTIVITE,
               EMP_EMAIL, EMP_TEL, EMP_DTAFF, EMP_DIDACT, EMP_ADRESSE,
               EMP_VILLE, EMP_CODE_POSTAL, EMP_STATUT, CREATED_AT, UPDATED_AT
        FROM EMPLOYEUR
        """
        params = {}
        
        if search:
            query += " WHERE UPPER(EMP_RAIS) LIKE UPPER(:search) OR UPPER(EMP_CLE) LIKE UPPER(:search) OR UPPER(EMP_SIGLE) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"

        query += " ORDER BY CREATED_AT DESC"
        
        if limit:
            if offset:
                query += " OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY"
                params['offset'] = offset
            else:
                query += " FETCH FIRST :limit ROWS ONLY"
            params['limit'] = limit
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                result = []
                for row in rows:
                    employeur = OracleDAO.dict_from_row(cursor, row)
                    # Convert dates to ISO format
                    if employeur.get('created_at'):
                        employeur['created_at'] = employeur['created_at'].isoformat()
                    if employeur.get('updated_at'):
                        employeur['updated_at'] = employeur['updated_at'].isoformat()
                    result.append(employeur)
                
                cursor.close()
                return result
        except Exception as e:
            logger.error(f"Failed to get employeurs: {e}")
            raise
    
    @staticmethod
    def get_by_id(employeur_id):
        """Get employeur by ID"""
        query = """
        SELECT EMP_MAT, EMP_CLE, EMP_RAIS, EMP_SIGLE, EMP_ACTIVITE,
               EMP_EMAIL, EMP_TEL, EMP_DTAFF, EMP_DIDACT, EMP_ADRESSE,
               EMP_VILLE, EMP_CODE_POSTAL, EMP_STATUT, CREATED_AT, UPDATED_AT
        FROM EMPLOYEUR WHERE EMP_MAT = :emp_mat
        """
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, {'emp_mat': employeur_id})
                row = cursor.fetchone()
                
                if row:
                    employeur = OracleDAO.dict_from_row(cursor, row)
                    # Convert dates to ISO format
                    if employeur.get('created_at'):
                        employeur['created_at'] = employeur['created_at'].isoformat()
                    if employeur.get('updated_at'):
                        employeur['updated_at'] = employeur['updated_at'].isoformat()
                    cursor.close()
                    return employeur
                
                cursor.close()
                return None
        except Exception as e:
            logger.error(f"Failed to get employeur {employeur_id}: {e}")
            raise
    
    @staticmethod
    def create(data):
        """Create new employeur"""
        query = """
        INSERT INTO EMPLOYEUR (EMP_MAT, EMP_CLE, EMP_RAIS, EMP_SIGLE, EMP_ACTIVITE,
                              EMP_EMAIL, EMP_TEL, EMP_DTAFF, EMP_DIDACT, EMP_ADRESSE,
                              EMP_VILLE, EMP_CODE_POSTAL, EMP_STATUT)
        VALUES (:emp_mat, :emp_cle, :emp_rais, :emp_sigle, :emp_activite,
                :emp_email, :emp_tel, :emp_dtaff, :emp_didact, :emp_adresse,
                :emp_ville, :emp_code_postal, :emp_statut)
        """

        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, data)
                connection.commit()
                cursor.close()

                return data.get('emp_mat')
        except Exception as e:
            logger.error(f"Failed to create employeur: {e}")
            raise
    
    @staticmethod
    def update(employeur_id, data):
        """Update employeur"""
        query = """
        UPDATE EMPLOYEUR
        SET EMP_CLE = :emp_cle, EMP_RAIS = :emp_rais, EMP_SIGLE = :emp_sigle,
            EMP_ACTIVITE = :emp_activite, EMP_EMAIL = :emp_email, EMP_TEL = :emp_tel,
            EMP_DTAFF = :emp_dtaff, EMP_DIDACT = :emp_didact, EMP_ADRESSE = :emp_adresse,
            EMP_VILLE = :emp_ville, EMP_CODE_POSTAL = :emp_code_postal,
            EMP_STATUT = :emp_statut, UPDATED_AT = CURRENT_TIMESTAMP
        WHERE EMP_MAT = :emp_mat
        """
        
        data['emp_mat'] = employeur_id
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, data)
                connection.commit()
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"Failed to update employeur {employeur_id}: {e}")
            raise
    
    @staticmethod
    def delete(employeur_id):
        """Delete employeur"""
        query = "DELETE FROM EMPLOYEUR WHERE EMP_MAT = :emp_mat"

        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, {'emp_mat': employeur_id})
                connection.commit()
                cursor.close()
                return True
        except Exception as e:
            logger.error(f"Failed to delete employeur {employeur_id}: {e}")
            raise
    
    @staticmethod
    def count(search=None):
        """Count total employeurs"""
        query = "SELECT COUNT(*) FROM EMPLOYEUR"
        params = {}

        if search:
            query += " WHERE UPPER(EMP_RAIS) LIKE UPPER(:search) OR UPPER(EMP_CLE) LIKE UPPER(:search) OR UPPER(EMP_SIGLE) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                count = cursor.fetchone()[0]
                cursor.close()
                return count
        except Exception as e:
            logger.error(f"Failed to count employeurs: {e}")
            raise

class AssureDAO:
    """Data Access Object for Assure operations"""
    
    @staticmethod
    def get_all(search=None, limit=None, offset=None):
        """Get all assures with optional search and pagination"""
        query = """
        SELECT a.ASS_MAT, a.ASS_CLE, a.EMP_MAT, a.EMP_CLE, a.ASS_NOM,
               a.ASS_PRENOM, a.ASS_DTEFF, a.ASS_DTIMAT, a.ASS_RIB,
               a.ASS_CHREFS, a.ASS_CIN, a.ASS_DTNAISS, a.ASS_SEXE,
               a.ASS_TEL, a.ASS_EMAIL, a.ETAT_ENGAGEMENT, a.CREATED_AT, a.UPDATED_AT,
               e.EMP_RAIS
        FROM ASSURE a
        LEFT JOIN EMPLOYEUR e ON a.EMP_MAT = e.EMP_MAT
        """
        params = {}

        if search:
            query += " WHERE UPPER(a.ASS_NOM) LIKE UPPER(:search) OR UPPER(a.ASS_PRENOM) LIKE UPPER(:search) OR UPPER(a.ASS_CIN) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"

        query += " ORDER BY a.CREATED_AT DESC"
        
        if limit:
            if offset:
                query += " OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY"
                params['offset'] = offset
            else:
                query += " FETCH FIRST :limit ROWS ONLY"
            params['limit'] = limit
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                result = []
                for row in rows:
                    assure = OracleDAO.dict_from_row(cursor, row)
                    # Convert dates to ISO format
                    if assure.get('date_naissance'):
                        assure['date_naissance'] = assure['date_naissance'].isoformat()
                    if assure.get('created_at'):
                        assure['created_at'] = assure['created_at'].isoformat()
                    if assure.get('updated_at'):
                        assure['updated_at'] = assure['updated_at'].isoformat()
                    result.append(assure)
                
                cursor.close()
                return result
        except Exception as e:
            logger.error(f"Failed to get assures: {e}")
            raise
    
    @staticmethod
    def create(data):
        """Create new assure"""
        query = """
        INSERT INTO ASSURE (ASS_MAT, ASS_CLE, EMP_MAT, EMP_CLE, ASS_NOM,
                           ASS_PRENOM, ASS_DTEFF, ASS_DTIMAT, ASS_RIB,
                           ASS_CHREFS, ASS_CIN, ASS_DTNAISS, ASS_SEXE,
                           ASS_TEL, ASS_EMAIL, ETAT_ENGAGEMENT)
        VALUES (:ass_mat, :ass_cle, :emp_mat, :emp_cle, :ass_nom,
                :ass_prenom, :ass_dteff, :ass_dtimat, :ass_rib,
                :ass_chrefs, :ass_cin, :ass_dtnaiss, :ass_sexe,
                :ass_tel, :ass_email, :etat_engagement)
        """

        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, data)
                connection.commit()
                cursor.close()

                return data.get('ass_mat')
        except Exception as e:
            logger.error(f"Failed to create assure: {e}")
            raise
    
    @staticmethod
    def count(search=None):
        """Count total assures"""
        query = "SELECT COUNT(*) FROM ASSURE"
        params = {}

        if search:
            query += " WHERE UPPER(ASS_NOM) LIKE UPPER(:search) OR UPPER(ASS_PRENOM) LIKE UPPER(:search) OR UPPER(ASS_CIN) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"
        
        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                count = cursor.fetchone()[0]
                cursor.close()
                return count
        except Exception as e:
            logger.error(f"Failed to count assures: {e}")
            raise

class BeneficiaireDAO:
    """Data Access Object for Beneficiaire operations"""

    @staticmethod
    def get_all(search=None, limit=None, offset=None):
        """Get all beneficiaires with optional search and pagination"""
        query = """
        SELECT BEN_MAT, BEN_CLE, BEN_IDOCNSS, BEN_NOM, BEN_PRENOM,
               BEN_NOM_AR, BEN_PRN_AR, BEN_EMAIL, BEN_TEL, BEN_NUMID,
               BEN_TYPID, BEN_DTNAIS, BEN_DTDECES, BEN_SEXE, BEN_TYPE,
               BEN_STATUT, CREATED_AT, UPDATED_AT
        FROM BENEFICIAIRE
        """
        params = {}

        if search:
            query += " WHERE UPPER(BEN_NOM) LIKE UPPER(:search) OR UPPER(BEN_PRENOM) LIKE UPPER(:search) OR UPPER(BEN_NUMID) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"

        query += " ORDER BY CREATED_AT DESC"

        if limit:
            if offset:
                query += " OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY"
                params['offset'] = offset
            else:
                query += " FETCH FIRST :limit ROWS ONLY"
            params['limit'] = limit

        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()

                result = []
                for row in rows:
                    beneficiaire = OracleDAO.dict_from_row(cursor, row)
                    # Convert dates to ISO format
                    if beneficiaire.get('created_at'):
                        beneficiaire['created_at'] = beneficiaire['created_at'].isoformat()
                    if beneficiaire.get('updated_at'):
                        beneficiaire['updated_at'] = beneficiaire['updated_at'].isoformat()
                    if beneficiaire.get('ben_dtnais'):
                        beneficiaire['ben_dtnais'] = beneficiaire['ben_dtnais'].isoformat()
                    if beneficiaire.get('ben_dtdeces'):
                        beneficiaire['ben_dtdeces'] = beneficiaire['ben_dtdeces'].isoformat()
                    result.append(beneficiaire)

                cursor.close()
                return result
        except Exception as e:
            logger.error(f"Failed to get beneficiaires: {e}")
            raise

    @staticmethod
    def create(data):
        """Create new beneficiaire"""
        query = """
        INSERT INTO BENEFICIAIRE (BEN_MAT, BEN_CLE, BEN_IDOCNSS, BEN_NOM, BEN_PRENOM,
                                 BEN_NOM_AR, BEN_PRN_AR, BEN_EMAIL, BEN_TEL, BEN_NUMID,
                                 BEN_TYPID, BEN_DTNAIS, BEN_DTDECES, BEN_SEXE, BEN_TYPE,
                                 BEN_STATUT)
        VALUES (:ben_mat, :ben_cle, :ben_idocnss, :ben_nom, :ben_prenom,
                :ben_nom_ar, :ben_prn_ar, :ben_email, :ben_tel, :ben_numid,
                :ben_typid, :ben_dtnais, :ben_dtdeces, :ben_sexe, :ben_type,
                :ben_statut)
        """

        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, data)
                connection.commit()
                cursor.close()

                return data.get('ben_mat')
        except Exception as e:
            logger.error(f"Failed to create beneficiaire: {e}")
            raise

    @staticmethod
    def count(search=None):
        """Count total beneficiaires"""
        query = "SELECT COUNT(*) FROM BENEFICIAIRE"
        params = {}

        if search:
            query += " WHERE UPPER(BEN_NOM) LIKE UPPER(:search) OR UPPER(BEN_PRENOM) LIKE UPPER(:search) OR UPPER(BEN_NUMID) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"

        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                count = cursor.fetchone()[0]
                cursor.close()
                return count
        except Exception as e:
            logger.error(f"Failed to count beneficiaires: {e}")
            raise

class UserDAO:
    """Data Access Object for User operations"""

    @staticmethod
    def get_all(search=None, limit=None, offset=None):
        """Get all users with optional search and pagination"""
        query = """
        SELECT ID, EMAIL, FIRST_NAME, LAST_NAME, HASHED_PASSWORD,
               ROLE, IS_ACTIVE, IS_VERIFIED, CREATED_AT, UPDATED_AT, LAST_LOGIN
        FROM USERS
        """
        params = {}

        if search:
            query += " WHERE UPPER(EMAIL) LIKE UPPER(:search) OR UPPER(FIRST_NAME) LIKE UPPER(:search) OR UPPER(LAST_NAME) LIKE UPPER(:search)"
            params['search'] = f"%{search}%"

        query += " ORDER BY CREATED_AT DESC"

        if limit:
            if offset:
                query += " OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY"
                params['offset'] = offset
            else:
                query += " FETCH FIRST :limit ROWS ONLY"
            params['limit'] = limit

        try:
            with OracleDAO.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()

                result = []
                for row in rows:
                    user = OracleDAO.dict_from_row(cursor, row)
                    # Convert dates to ISO format and remove password
                    if user.get('created_at'):
                        user['created_at'] = user['created_at'].isoformat()
                    if user.get('updated_at'):
                        user['updated_at'] = user['updated_at'].isoformat()
                    if user.get('last_login'):
                        user['last_login'] = user['last_login'].isoformat()
                    # Remove password from response
                    user.pop('hashed_password', None)
                    result.append(user)

                cursor.close()
                return result
        except Exception as e:
            logger.error(f"Failed to get users: {e}")
            raise
