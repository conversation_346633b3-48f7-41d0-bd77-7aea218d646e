# Flask Configuration
SECRET_KEY=your-secret-key-change-this
JWT_SECRET_KEY=jwt-secret-string-change-this
FLASK_ENV=development

# Database Configuration
# Set DB_TYPE to 'oracle' to use Oracle database, 'sqlite' for SQLite (default)
DB_TYPE=sqlite

# SQLite Configuration (used when DB_TYPE=sqlite)
DATABASE_URL=sqlite:///cnss_users.db

# Oracle Database Configuration (used when DB_TYPE=oracle)
ORACLE_HOST=localhost
ORACLE_PORT=1521
ORACLE_SERVICE_NAME=XE
ORACLE_USERNAME=cnss_user
ORACLE_PASSWORD=password

# Optional: Enable SQL query logging
SQLALCHEMY_ECHO=False

# Legacy Oracle settings (for backward compatibility)
ORACLE_USER=cnss_user
ORACLE_DSN=localhost:1521/XE
