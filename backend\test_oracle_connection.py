#!/usr/bin/env python3
"""
Oracle Database Connection Test Script
Run this script to test your Oracle database connection before switching the main app to Oracle.

Usage:
    python test_oracle_connection.py
"""

import os
import sys
from dotenv import load_dotenv
import oracledb

# Load environment variables
load_dotenv()

def test_oracle_connection():
    """Test Oracle database connection"""
    
    # Get Oracle connection details from environment
    host = os.getenv('ORACLE_HOST', 'localhost')
    port = os.getenv('ORACLE_PORT', '1521')
    service_name = os.getenv('ORACLE_SERVICE_NAME', 'XEPDB1')
    username = os.getenv('ORACLE_USERNAME', 'cnss_user')
    password = os.getenv('ORACLE_PASSWORD', 'cnss_password')
    
    print("=" * 60)
    print("Oracle Database Connection Test")
    print("=" * 60)
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"Service Name: {service_name}")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    print("-" * 60)
    
    try:
        # Create connection string
        dsn = f"{host}:{port}/{service_name}"
        print(f"Connecting to: {dsn}")
        
        # Attempt connection
        connection = oracledb.connect(
            user=username,
            password=password,
            dsn=dsn
        )
        
        print("✅ Connection successful!")
        
        # Test basic query
        cursor = connection.cursor()
        cursor.execute("SELECT 'Hello from Oracle!' as message, SYSDATE as current_time FROM DUAL")
        result = cursor.fetchone()
        
        print(f"✅ Query test successful!")
        print(f"   Message: {result[0]}")
        print(f"   Current Time: {result[1]}")
        
        # Test if our tables exist
        print("\n📋 Checking for CNSS tables...")
        
        tables_to_check = ['EMPLOYEURS', 'ASSURES', 'BENEFICIAIRES']
        existing_tables = []
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                existing_tables.append(table)
                print(f"   ✅ {table}: {count} records")
            except Exception as e:
                print(f"   ❌ {table}: Not found ({str(e)[:50]}...)")
        
        if len(existing_tables) == len(tables_to_check):
            print("\n🎉 All required tables exist! Your Oracle database is ready.")
            print("\n💡 To switch the application to Oracle:")
            print("   1. Change DB_TYPE=oracle in backend/.env")
            print("   2. Restart the Flask backend")
        else:
            print(f"\n⚠️  Only {len(existing_tables)}/{len(tables_to_check)} tables found.")
            print("   Run the setup script to create missing tables:")
            print("   sqlplus cnss_user/cnss_password@localhost:1521/XEPDB1")
            print("   @database/oracle_setup.sql")
        
        cursor.close()
        connection.close()
        
        return True
        
    except oracledb.DatabaseError as e:
        error_obj, = e.args
        print(f"❌ Database Error: {error_obj.message}")
        
        if "ORA-01017" in str(error_obj.message):
            print("\n🔧 Fix: Check your username and password")
            print("   - Verify ORACLE_USERNAME and ORACLE_PASSWORD in .env")
            print("   - Make sure the user exists in Oracle")
            
        elif "ORA-12541" in str(error_obj.message):
            print("\n🔧 Fix: Check your connection details")
            print("   - Verify ORACLE_HOST and ORACLE_PORT in .env")
            print("   - Make sure Oracle database is running")
            print("   - Try: docker start oracle-xe")
            
        elif "ORA-12514" in str(error_obj.message):
            print("\n🔧 Fix: Check your service name")
            print("   - Verify ORACLE_SERVICE_NAME in .env")
            print("   - Common values: XE, ORCL, XEPDB1, FREEPDB1")
            
        return False
        
    except Exception as e:
        print(f"❌ Connection Error: {str(e)}")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure Oracle database is running")
        print("   2. Check your .env file configuration")
        print("   3. Verify network connectivity")
        return False

def main():
    """Main function"""
    success = test_oracle_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Oracle connection test PASSED!")
        print("Your database is ready for the CNSS application.")
    else:
        print("❌ Oracle connection test FAILED!")
        print("Please fix the issues above before proceeding.")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
