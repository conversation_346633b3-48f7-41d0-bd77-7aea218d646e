"""
Database utility functions for handling both SQLite and Oracle connections
"""
import os
import logging
from flask import current_app
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import oracledb

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self, app=None):
        self.app = app
        self.oracle_pool = None
        
    def init_app(self, app):
        """Initialize the database manager with Flask app"""
        self.app = app
        
        # Initialize Oracle connection pool if using Oracle
        if app.config.get('DB_TYPE', '').lower() == 'oracle':
            self.init_oracle_pool()
    
    def init_oracle_pool(self):
        """Initialize Oracle connection pool"""
        try:
            # Create Oracle connection pool
            self.oracle_pool = oracledb.create_pool(
                user=current_app.config['ORACLE_USERNAME'],
                password=current_app.config['ORACLE_PASSWORD'],
                dsn=f"{current_app.config['ORACLE_HOST']}:{current_app.config['ORACLE_PORT']}/{current_app.config['ORACLE_SERVICE_NAME']}",
                min=2,
                max=10,
                increment=1
            )
            logger.info("Oracle connection pool initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Oracle connection pool: {e}")
            raise
    
    def get_oracle_connection(self):
        """Get Oracle connection from pool"""
        if not self.oracle_pool:
            raise RuntimeError("Oracle connection pool not initialized")
        return self.oracle_pool.acquire()
    
    def test_connection(self):
        """Test database connection"""
        try:
            if current_app.config.get('DB_TYPE', '').lower() == 'oracle':
                return self.test_oracle_connection()
            else:
                return self.test_sqlite_connection()
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False, str(e)
    
    def test_oracle_connection(self):
        """Test Oracle database connection"""
        try:
            with self.get_oracle_connection() as connection:
                cursor = connection.cursor()
                cursor.execute("SELECT 1 FROM DUAL")
                result = cursor.fetchone()
                cursor.close()
                if result:
                    return True, "Oracle connection successful"
                else:
                    return False, "Oracle connection test failed"
        except Exception as e:
            return False, f"Oracle connection error: {e}"
    
    def test_sqlite_connection(self):
        """Test SQLite database connection"""
        try:
            from app import db
            # Test SQLAlchemy connection
            db.engine.execute(text("SELECT 1"))
            return True, "SQLite connection successful"
        except Exception as e:
            return False, f"SQLite connection error: {e}"

# Global database manager instance
db_manager = DatabaseManager()

def create_oracle_tables():
    """Create Oracle tables if they don't exist"""
    if current_app.config.get('DB_TYPE', '').lower() != 'oracle':
        return
    
    try:
        with db_manager.get_oracle_connection() as connection:
            cursor = connection.cursor()
            
            # Create sequences
            sequences = [
                "CREATE SEQUENCE employeur_seq START WITH 1 INCREMENT BY 1",
                "CREATE SEQUENCE assure_seq START WITH 1 INCREMENT BY 1", 
                "CREATE SEQUENCE beneficiaire_seq START WITH 1 INCREMENT BY 1"
            ]
            
            for seq_sql in sequences:
                try:
                    cursor.execute(seq_sql)
                except Exception as e:
                    if "name is already used" not in str(e).lower():
                        logger.warning(f"Failed to create sequence: {e}")
            
            # Create tables
            tables = [
                """
                CREATE TABLE employeurs (
                    id NUMBER PRIMARY KEY,
                    nom_entreprise VARCHAR2(255) NOT NULL,
                    numero_cnss VARCHAR2(50) UNIQUE NOT NULL,
                    adresse CLOB,
                    ville VARCHAR2(100),
                    telephone VARCHAR2(20),
                    email VARCHAR2(255),
                    secteur_activite VARCHAR2(255),
                    statut VARCHAR2(20) DEFAULT 'Actif',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """,
                """
                CREATE TABLE assures (
                    id NUMBER PRIMARY KEY,
                    nom VARCHAR2(100) NOT NULL,
                    prenom VARCHAR2(100) NOT NULL,
                    cin VARCHAR2(20) UNIQUE NOT NULL,
                    numero_assure VARCHAR2(50) UNIQUE NOT NULL,
                    date_naissance DATE,
                    lieu_naissance VARCHAR2(255),
                    adresse CLOB,
                    telephone VARCHAR2(20),
                    email VARCHAR2(255),
                    employeur_id NUMBER,
                    statut VARCHAR2(20) DEFAULT 'Actif',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employeur_id) REFERENCES employeurs(id)
                )
                """,
                """
                CREATE TABLE beneficiaires (
                    id NUMBER PRIMARY KEY,
                    nom VARCHAR2(100) NOT NULL,
                    prenom VARCHAR2(100) NOT NULL,
                    cin VARCHAR2(20),
                    date_naissance DATE,
                    relation_assure VARCHAR2(50) NOT NULL,
                    assure_id NUMBER NOT NULL,
                    numero_beneficiaire VARCHAR2(50),
                    statut VARCHAR2(20) DEFAULT 'Actif',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (assure_id) REFERENCES assures(id)
                )
                """
            ]
            
            for table_sql in tables:
                try:
                    cursor.execute(table_sql)
                    logger.info(f"Table created successfully")
                except Exception as e:
                    if "name is already used" not in str(e).lower():
                        logger.warning(f"Failed to create table: {e}")
            
            # Create triggers for auto-increment
            triggers = [
                """
                CREATE OR REPLACE TRIGGER employeur_trigger
                BEFORE INSERT ON employeurs
                FOR EACH ROW
                BEGIN
                    IF :NEW.id IS NULL THEN
                        SELECT employeur_seq.NEXTVAL INTO :NEW.id FROM DUAL;
                    END IF;
                END;
                """,
                """
                CREATE OR REPLACE TRIGGER assure_trigger
                BEFORE INSERT ON assures
                FOR EACH ROW
                BEGIN
                    IF :NEW.id IS NULL THEN
                        SELECT assure_seq.NEXTVAL INTO :NEW.id FROM DUAL;
                    END IF;
                END;
                """,
                """
                CREATE OR REPLACE TRIGGER beneficiaire_trigger
                BEFORE INSERT ON beneficiaires
                FOR EACH ROW
                BEGIN
                    IF :NEW.id IS NULL THEN
                        SELECT beneficiaire_seq.NEXTVAL INTO :NEW.id FROM DUAL;
                    END IF;
                END;
                """
            ]
            
            for trigger_sql in triggers:
                try:
                    cursor.execute(trigger_sql)
                    logger.info("Trigger created successfully")
                except Exception as e:
                    logger.warning(f"Failed to create trigger: {e}")
            
            connection.commit()
            cursor.close()
            logger.info("Oracle tables created successfully")
            
    except Exception as e:
        logger.error(f"Failed to create Oracle tables: {e}")
        raise

def init_database(app):
    """Initialize database based on configuration"""
    db_manager.init_app(app)
    
    if app.config.get('DB_TYPE', '').lower() == 'oracle':
        logger.info("Initializing Oracle database...")
        create_oracle_tables()
    else:
        logger.info("Using SQLite database...")
        # SQLAlchemy will handle SQLite table creation
    
    # Test connection
    success, message = db_manager.test_connection()
    if success:
        logger.info(f"Database initialized successfully: {message}")
    else:
        logger.error(f"Database initialization failed: {message}")
        raise RuntimeError(f"Database connection failed: {message}")
