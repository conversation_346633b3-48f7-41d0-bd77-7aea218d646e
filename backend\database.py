"""
Database utility functions for handling both SQLite and Oracle connections
"""
import os
import logging
from flask import current_app
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import oracledb

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self, app=None):
        self.app = app
        self.oracle_pool = None
        
    def init_app(self, app):
        """Initialize the database manager with Flask app"""
        self.app = app
        
        # Initialize Oracle connection pool if using Oracle
        if app.config.get('DB_TYPE', '').lower() == 'oracle':
            self.init_oracle_pool()
    
    def init_oracle_pool(self):
        """Initialize Oracle connection pool"""
        try:
            # Create Oracle connection pool
            self.oracle_pool = oracledb.create_pool(
                user=self.app.config['ORACLE_USERNAME'],
                password=self.app.config['ORACLE_PASSWORD'],
                dsn=f"{self.app.config['ORACLE_HOST']}:{self.app.config['ORACLE_PORT']}/{self.app.config['ORACLE_SERVICE_NAME']}",
                min=2,
                max=10,
                increment=1
            )
            logger.info("Oracle connection pool initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Oracle connection pool: {e}")
            raise
    
    def get_oracle_connection(self):
        """Get Oracle connection from pool"""
        if not self.oracle_pool:
            raise RuntimeError("Oracle connection pool not initialized")
        return self.oracle_pool.acquire()
    
    def test_connection(self, app=None):
        """Test database connection"""
        try:
            if app is None:
                app = self.app
            if app.config.get('DB_TYPE', '').lower() == 'oracle':
                return self.test_oracle_connection()
            else:
                return self.test_sqlite_connection()
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False, str(e)
    
    def test_oracle_connection(self):
        """Test Oracle database connection"""
        try:
            with self.get_oracle_connection() as connection:
                cursor = connection.cursor()
                cursor.execute("SELECT 1 FROM DUAL")
                result = cursor.fetchone()
                cursor.close()
                if result:
                    return True, "Oracle connection successful"
                else:
                    return False, "Oracle connection test failed"
        except Exception as e:
            return False, f"Oracle connection error: {e}"
    
    def test_sqlite_connection(self):
        """Test SQLite database connection"""
        try:
            from app import db
            # Test SQLAlchemy connection
            db.engine.execute(text("SELECT 1"))
            return True, "SQLite connection successful"
        except Exception as e:
            return False, f"SQLite connection error: {e}"

# Global database manager instance
db_manager = DatabaseManager()

def create_oracle_tables(app=None):
    """Test Oracle connection and verify tables exist"""
    if app is None:
        app = current_app
    if app.config.get('DB_TYPE', '').lower() != 'oracle':
        return

    try:
        with db_manager.get_oracle_connection() as connection:
            cursor = connection.cursor()

            # Test connection by checking if our tables exist
            test_queries = [
                "SELECT COUNT(*) FROM EMPLOYEUR WHERE ROWNUM <= 1",
                "SELECT COUNT(*) FROM ASSURE WHERE ROWNUM <= 1",
                "SELECT COUNT(*) FROM BENEFICIAIRE WHERE ROWNUM <= 1",
                "SELECT COUNT(*) FROM USERS WHERE ROWNUM <= 1"
            ]

            for query in test_queries:
                try:
                    cursor.execute(query)
                    cursor.fetchone()
                    logger.info(f"Table verified: {query.split('FROM')[1].split('WHERE')[0].strip()}")
                except Exception as e:
                    logger.warning(f"Table check failed: {e}")

            cursor.close()
            logger.info("Oracle database connection verified successfully")

    except Exception as e:
        logger.error(f"Failed to verify Oracle tables: {e}")
        raise

def init_database(app):
    """Initialize database based on configuration"""
    db_manager.init_app(app)
    
    if app.config.get('DB_TYPE', '').lower() == 'oracle':
        logger.info("Initializing Oracle database...")
        create_oracle_tables(app)
    else:
        logger.info("Using SQLite database...")
        # SQLAlchemy will handle SQLite table creation
    
    # Test connection
    success, message = db_manager.test_connection(app)
    if success:
        logger.info(f"Database initialized successfully: {message}")
    else:
        logger.error(f"Database initialization failed: {message}")
        raise RuntimeError(f"Database connection failed: {message}")
