from functools import wraps
from flask import jsonify
from flask_jwt_extended import get_jwt_identity, get_jwt
from models import User

def admin_required(f):
    """Decorator to require admin role for a route"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user or user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403
            
        return f(*args, **kwargs)
    return decorated_function

def agent_or_admin_required(f):
    """Decorator to require agent or admin role for a route"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user or user.role not in ['agent', 'admin']:
            return jsonify({'error': 'Agent or Admin access required'}), 403
            
        return f(*args, **kwargs)
    return decorated_function

def get_current_user():
    """Get the current authenticated user"""
    current_user_id = get_jwt_identity()
    return User.query.get(current_user_id)

def log_user_action(user_id, action, table_name=None, record_id=None, details=None, ip_address=None):
    """Log user actions for audit trail"""
    from models import AuditLog, db
    
    log_entry = AuditLog(
        user_id=user_id,
        action=action,
        table_name=table_name,
        record_id=record_id,
        details=details,
        ip_address=ip_address
    )
    db.session.add(log_entry)
    db.session.commit()
    return log_entry
