import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Activity,
  Search,
  Filter,
  Download,
  Calendar,
  User,
  Database,
  Shield,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
} from "lucide-react";
import DashboardLayout from "../components/layout/DashboardLayout";
import { useAuth } from "../contexts/AuthContext";
import { auditAPI } from "../utils/api";

const ActionIcon = ({ action }) => {
  const iconProps = { className: "w-4 h-4" };

  switch (action) {
    case "LOGIN":
      return <CheckCircle {...iconProps} className="w-4 h-4 text-green-500" />;
    case "USER_CREATED":
    case "USER_UPDATED":
    case "USER_DELETED":
      return <User {...iconProps} className="w-4 h-4 text-blue-500" />;
    case "EMPLOYEUR_CREATED":
    case "ASSURE_CREATED":
    case "BENEFICIAIRE_CREATED":
      return <Database {...iconProps} className="w-4 h-4 text-green-500" />;
    case "EMPLOYEUR_UPDATED":
    case "ASSURE_UPDATED":
    case "BENEFICIAIRE_UPDATED":
      return <Database {...iconProps} className="w-4 h-4 text-yellow-500" />;
    case "EMPLOYEUR_DELETED":
    case "ASSURE_DELETED":
    case "BENEFICIAIRE_DELETED":
      return <Database {...iconProps} className="w-4 h-4 text-red-500" />;
    default:
      return <Activity {...iconProps} className="w-4 h-4 text-gray-500" />;
  }
};

const LogEntry = ({ log, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: index * 0.05 }}
    className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
  >
    <div className="flex items-start justify-between">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-1">
          <ActionIcon action={log.action} />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h4 className="text-sm font-medium text-gray-900">
              {log.action.replace(/_/g, " ")}
            </h4>
            <span
              className={`px-2 py-1 text-xs font-medium rounded-full ${
                log.action.includes("DELETE")
                  ? "bg-red-100 text-red-800"
                  : log.action.includes("CREATE")
                  ? "bg-green-100 text-green-800"
                  : log.action.includes("UPDATE")
                  ? "bg-yellow-100 text-yellow-800"
                  : "bg-blue-100 text-blue-800"
              }`}
            >
              {log.table_name || "System"}
            </span>
          </div>
          <p className="text-sm text-gray-600 mt-1">{log.details}</p>
          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <User className="w-3 h-3" />
              <span>{log.username || "Unknown"}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>{new Date(log.timestamp).toLocaleString()}</span>
            </div>
            {log.ip_address && (
              <div className="flex items-center space-x-1">
                <Shield className="w-3 h-3" />
                <span>{log.ip_address}</span>
              </div>
            )}
          </div>
        </div>
      </div>
      <button className="text-gray-400 hover:text-gray-600 p-1">
        <Eye className="w-4 h-4" />
      </button>
    </div>
  </motion.div>
);

const AuditLogs = () => {
  const { isAdmin } = useAuth();
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterAction, setFilterAction] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    if (isAdmin()) {
      loadLogs();
    }
  }, [currentPage, filterAction]);

  const loadLogs = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: 20,
      };

      if (searchTerm.trim()) {
        params.search = searchTerm.trim();
      }

      if (filterAction) {
        params.action = filterAction;
      }

      const response = await auditAPI.getLogs(params);
      setLogs(response.logs || []);
      setTotalPages(response.pages || 1);
    } catch (error) {
      console.error("Failed to load audit logs:", error);
      // Mock data for demonstration
      setLogs([
        {
          id: 1,
          user_id: 1,
          username: "admin",
          action: "LOGIN",
          details: "User admin logged in",
          timestamp: new Date().toISOString(),
          ip_address: "*************",
        },
        {
          id: 2,
          user_id: 1,
          username: "admin",
          action: "EMPLOYEUR_CREATED",
          table_name: "employeur",
          record_id: "1",
          details: "Created employeur CNSS Maroc",
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          ip_address: "*************",
        },
        {
          id: 3,
          user_id: 1,
          username: "admin",
          action: "USER_CREATED",
          table_name: "user",
          record_id: "2",
          details: "Created user agent_user with role agent",
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          ip_address: "*************",
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
    loadLogs();
  };

  const handleExport = () => {
    alert("Export functionality will be implemented in the next phase");
  };

  const actionTypes = [
    "LOGIN",
    "USER_CREATED",
    "USER_UPDATED",
    "USER_DELETED",
    "EMPLOYEUR_CREATED",
    "EMPLOYEUR_UPDATED",
    "EMPLOYEUR_DELETED",
    "ASSURE_CREATED",
    "ASSURE_UPDATED",
    "ASSURE_DELETED",
    "BENEFICIAIRE_CREATED",
    "BENEFICIAIRE_UPDATED",
    "BENEFICIAIRE_DELETED",
  ];

  if (!isAdmin()) {
    return (
      <DashboardLayout currentPage="logs">
        <div className="text-center py-12">
          <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Access Denied
          </h2>
          <p className="text-gray-600">
            You need admin privileges to access audit logs.
          </p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout currentPage="logs">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Audit Logs</h1>
            <p className="text-gray-600 mt-1">
              Track all system activities and user actions
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleExport}
              className="flex items-center px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
            <button className="flex items-center px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
              <Calendar className="w-4 h-4 mr-2" />
              Date Range
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Activity className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Total Actions
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {logs.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Today's Logins
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {logs.filter((log) => log.action === "LOGIN").length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Database className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Data Changes
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {
                    logs.filter(
                      (log) => log.table_name && log.table_name !== "user"
                    ).length
                  }
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertCircle className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Deletions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {logs.filter((log) => log.action.includes("DELETE")).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search logs by user, action, or details..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select
              value={filterAction}
              onChange={(e) => setFilterAction(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Actions</option>
              {actionTypes.map((action) => (
                <option key={action} value={action}>
                  {action.replace(/_/g, " ")}
                </option>
              ))}
            </select>
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Search
            </button>
          </div>
        </div>

        {/* Logs List */}
        <div className="space-y-4">
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div
                  key={i}
                  className="bg-white border border-gray-200 rounded-lg p-4 animate-pulse"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
                      <div className="h-3 bg-gray-300 rounded w-3/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-12">
              <Activity className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No audit logs found
              </h3>
              <p className="text-gray-600">
                No activities match your current filters.
              </p>
            </div>
          ) : (
            logs.map((log, index) => (
              <LogEntry key={log.id} log={log} index={index} />
            ))
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between bg-white px-6 py-3 border border-gray-200 rounded-lg">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              <button
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default AuditLogs;
