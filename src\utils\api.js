const API_BASE_URL = 'http://localhost:5000/api';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` })
  };
};

// Generic API call function
const apiCall = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const config = {
    headers: getAuthHeaders(),
    ...options
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
};

// Authentication API calls
export const authAPI = {
  login: (credentials) => 
    apiCall('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    }),

  register: (userData) =>
    apiCall('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData)
    }),

  getCurrentUser: () =>
    apiCall('/auth/me'),

  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }
};

// User management API calls
export const userAPI = {
  getUsers: () => apiCall('/users'),
  
  createUser: (userData) =>
    apiCall('/users', {
      method: 'POST',
      body: JSON.stringify(userData)
    }),

  updateUser: (userId, userData) =>
    apiCall(`/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData)
    }),

  deleteUser: (userId) =>
    apiCall(`/users/${userId}`, {
      method: 'DELETE'
    })
};

// Audit log API calls
export const auditAPI = {
  getLogs: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/audit-logs${queryString ? `?${queryString}` : ''}`);
  }
};

// Health check
export const healthAPI = {
  check: () => apiCall('/health')
};

export default {
  authAPI,
  userAPI,
  auditAPI,
  healthAPI
};
