const API_BASE_URL = "http://localhost:5000/api";

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem("token");
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// Generic API call function
const apiCall = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const config = {
    headers: getAuthHeaders(),
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("API call failed:", error);
    throw error;
  }
};

// Authentication API calls
export const authAPI = {
  login: (credentials) =>
    apiCall("/auth/login", {
      method: "POST",
      body: JSON.stringify(credentials),
    }),

  register: (userData) =>
    apiCall("/auth/register", {
      method: "POST",
      body: JSON.stringify(userData),
    }),

  getCurrentUser: () => apiCall("/auth/me"),

  logout: () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
  },
};

// User management API calls
export const userAPI = {
  getUsers: () => apiCall("/users"),

  createUser: (userData) =>
    apiCall("/auth/register", {
      method: "POST",
      body: JSON.stringify(userData),
    }),

  updateUser: (userId, userData) =>
    apiCall(`/users/${userId}`, {
      method: "PUT",
      body: JSON.stringify(userData),
    }),

  deleteUser: (userId) =>
    apiCall(`/users/${userId}`, {
      method: "DELETE",
    }),
};

// Audit log API calls
export const auditAPI = {
  getLogs: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/audit-logs${queryString ? `?${queryString}` : ""}`);
  },
};

// Data management API calls
export const dataAPI = {
  // Employeur endpoints
  getEmployeurs: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/employeurs${queryString ? `?${queryString}` : ""}`);
  },

  createEmployeur: (data) =>
    apiCall("/employeurs", {
      method: "POST",
      body: JSON.stringify(data),
    }),

  updateEmployeur: (id, data) =>
    apiCall(`/employeurs/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    }),

  deleteEmployeur: (id) =>
    apiCall(`/employeurs/${id}`, {
      method: "DELETE",
    }),

  // Assure endpoints
  getAssures: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/assures${queryString ? `?${queryString}` : ""}`);
  },

  createAssure: (data) =>
    apiCall("/assures", {
      method: "POST",
      body: JSON.stringify(data),
    }),

  updateAssure: (id, data) =>
    apiCall(`/assures/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    }),

  deleteAssure: (id) =>
    apiCall(`/assures/${id}`, {
      method: "DELETE",
    }),

  // Beneficiaire endpoints
  getBeneficiaires: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiCall(`/beneficiaires${queryString ? `?${queryString}` : ""}`);
  },

  createBeneficiaire: (data) =>
    apiCall("/beneficiaires", {
      method: "POST",
      body: JSON.stringify(data),
    }),

  updateBeneficiaire: (id, data) =>
    apiCall(`/beneficiaires/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    }),

  deleteBeneficiaire: (id) =>
    apiCall(`/beneficiaires/${id}`, {
      method: "DELETE",
    }),
};

// Health check
export const healthAPI = {
  check: () => apiCall("/health"),
};

export default {
  authAPI,
  userAPI,
  auditAPI,
  dataAPI,
  healthAPI,
};
