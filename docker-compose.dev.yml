version: '3.8'

services:
  # Backend service for development
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - SECRET_KEY=dev-secret-key
      - JWT_SECRET_KEY=dev-jwt-secret
      - DATABASE_URL=sqlite:///data/cnss_users.db
      - DEBUG=True
    volumes:
      - ./backend:/app
      - backend_data:/app/data
    networks:
      - cnss-dev-network
    restart: unless-stopped
    command: python app.py

  # Frontend development server
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5000
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - cnss-dev-network
    restart: unless-stopped

volumes:
  backend_data:
    driver: local

networks:
  cnss-dev-network:
    driver: bridge
