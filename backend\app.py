from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, jwt_required, create_access_token, get_jwt_identity
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta, date
import os
import logging
from config import get_config
from database import init_database, db_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Load configuration
config_class = get_config()
app.config.from_object(config_class)

logger.info(f"Using database type: {app.config.get('DB_TYPE', 'sqlite')}")
logger.info(f"Database URI: {app.config['SQLALCHEMY_DATABASE_URI']}")

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app)

# Initialize database
try:
    init_database(app)
except Exception as e:
    logger.error(f"Database initialization failed: {e}")
    logger.info("Falling back to SQLite...")
    # Fallback to SQLite if Oracle fails
    app.config['DB_TYPE'] = 'sqlite'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///cnss_users.db'

# Database Models
# User model for authentication
class User(db.Model):
    __tablename__ = 'USERS'

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column('EMAIL', db.String(255), unique=True, nullable=False)
    first_name = db.Column('FIRST_NAME', db.String(100), nullable=False)
    last_name = db.Column('LAST_NAME', db.String(100), nullable=False)
    hashed_password = db.Column('HASHED_PASSWORD', db.String(255), nullable=False)
    role = db.Column('ROLE', db.String(5), nullable=False, default='agent')
    is_active = db.Column('IS_ACTIVE', db.Integer, default=1)
    is_verified = db.Column('IS_VERIFIED', db.Integer, default=0)
    created_at = db.Column('CREATED_AT', db.DateTime, default=datetime.utcnow)
    updated_at = db.Column('UPDATED_AT', db.DateTime, default=datetime.utcnow)
    last_login = db.Column('LAST_LOGIN', db.DateTime)

    def set_password(self, password):
        self.hashed_password = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.hashed_password, password)

    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'role': self.role,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

# Audit log model
class AuditLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('USERS.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)
    table_name = db.Column(db.String(50))
    record_id = db.Column(db.String(50))
    details = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(45))

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'action': self.action,
            'table_name': self.table_name,
            'record_id': self.record_id,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'ip_address': self.ip_address
        }

# Oracle database models for business data
class Employeur(db.Model):
    __tablename__ = 'employeur'
    id = db.Column(db.Integer, primary_key=True)
    nom_entreprise = db.Column(db.String(200), nullable=False)
    numero_cnss = db.Column(db.String(50), unique=True, nullable=False)
    adresse = db.Column(db.String(500))
    ville = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    secteur_activite = db.Column(db.String(100))
    nombre_employes = db.Column(db.Integer)
    date_affiliation = db.Column(db.Date)
    statut = db.Column(db.String(20), default='actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'nom_entreprise': self.nom_entreprise,
            'numero_cnss': self.numero_cnss,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'email': self.email,
            'secteur_activite': self.secteur_activite,
            'nombre_employes': self.nombre_employes,
            'date_affiliation': self.date_affiliation.isoformat() if self.date_affiliation else None,
            'statut': self.statut,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Assure(db.Model):
    __tablename__ = 'assure'
    id = db.Column(db.Integer, primary_key=True)
    numero_assure = db.Column(db.String(50), unique=True, nullable=False)
    cin = db.Column(db.String(20), unique=True, nullable=False)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    date_naissance = db.Column(db.Date)
    lieu_naissance = db.Column(db.String(100))
    sexe = db.Column(db.String(1))
    adresse = db.Column(db.String(500))
    ville = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    employeur_id = db.Column(db.Integer, db.ForeignKey('employeur.id'))
    date_affiliation = db.Column(db.Date)
    statut = db.Column(db.String(20), default='actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    employeur = db.relationship('Employeur', backref=db.backref('assures', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'numero_assure': self.numero_assure,
            'cin': self.cin,
            'nom': self.nom,
            'prenom': self.prenom,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'lieu_naissance': self.lieu_naissance,
            'sexe': self.sexe,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'email': self.email,
            'employeur_id': self.employeur_id,
            'employeur_nom': self.employeur.nom_entreprise if self.employeur else None,
            'date_affiliation': self.date_affiliation.isoformat() if self.date_affiliation else None,
            'statut': self.statut,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Beneficiaire(db.Model):
    __tablename__ = 'beneficiaire'
    id = db.Column(db.Integer, primary_key=True)
    numero_beneficiaire = db.Column(db.String(50), unique=True, nullable=False)
    assure_id = db.Column(db.Integer, db.ForeignKey('assure.id'), nullable=False)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    date_naissance = db.Column(db.Date)
    sexe = db.Column(db.String(1))
    relation_assure = db.Column(db.String(50))  # conjoint, enfant, parent, etc.
    adresse = db.Column(db.String(500))
    ville = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    statut = db.Column(db.String(20), default='actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    assure = db.relationship('Assure', backref=db.backref('beneficiaires', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'numero_beneficiaire': self.numero_beneficiaire,
            'assure_id': self.assure_id,
            'assure_nom': f"{self.assure.nom} {self.assure.prenom}" if self.assure else None,
            'nom': self.nom,
            'prenom': self.prenom,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'sexe': self.sexe,
            'relation_assure': self.relation_assure,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'statut': self.statut,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

# Authentication routes
@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        email = data.get('email') or data.get('username')  # Support both email and username
        password = data.get('password')

        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400

        user = User.query.filter_by(email=email, is_active=1).first()

        if user and user.check_password(password):
            # Update last login
            user.last_login = datetime.utcnow()
            db.session.commit()

            access_token = create_access_token(
                identity=user.id,
                additional_claims={
                    'email': user.email,
                    'role': user.role,
                    'first_name': user.first_name,
                    'last_name': user.last_name
                }
            )
            
            # Log the login
            log_entry = AuditLog(
                user_id=user.id,
                action='LOGIN',
                details=f'User {user.email} logged in',
                ip_address=request.remote_addr
            )
            db.session.add(log_entry)
            db.session.commit()

            return jsonify({
                'access_token': access_token,
                'user': user.to_dict()
            }), 200
        else:
            return jsonify({'error': 'Invalid credentials'}), 401

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/register', methods=['POST'])
@jwt_required()
def register():
    try:
        # Only admins can register new users
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)
        
        if current_user.role != 'admin':
            return jsonify({'error': 'Only admins can register new users'}), 403

        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        role = data.get('role', 'agent')
        organization = data.get('organization')

        if not all([username, email, password, organization]):
            return jsonify({'error': 'All fields are required'}), 400

        # Check if user already exists
        if User.query.filter_by(username=username).first():
            return jsonify({'error': 'Username already exists'}), 400

        if User.query.filter_by(email=email).first():
            return jsonify({'error': 'Email already exists'}), 400

        # Create new user
        new_user = User(
            username=username,
            email=email,
            role=role,
            organization=organization
        )
        new_user.set_password(password)

        db.session.add(new_user)
        db.session.commit()

        # Log the registration
        log_entry = AuditLog(
            user_id=current_user_id,
            action='USER_CREATED',
            details=f'Created user {username} with role {role}',
            ip_address=request.remote_addr
        )
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'message': 'User created successfully', 'user': new_user.to_dict()}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/me', methods=['GET'])
@jwt_required()
def get_current_user():
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({'user': user.to_dict()}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """Change user password"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        if not user:
            return jsonify({'message': 'User not found'}), 404

        data = request.get_json()
        current_password = data.get('currentPassword')
        new_password = data.get('newPassword')

        if not current_password or not new_password:
            return jsonify({'message': 'Current password and new password are required'}), 400

        # Verify current password
        if not user.check_password(current_password):
            return jsonify({'message': 'Current password is incorrect'}), 400

        # Validate new password
        if len(new_password) < 6:
            return jsonify({'message': 'New password must be at least 6 characters long'}), 400

        # Update password
        user.set_password(new_password)
        db.session.commit()

        # Log the action
        log_user_action(
            user_id=user.id,
            username=user.username,
            action='PASSWORD_CHANGE',
            table_name='user',
            record_id=user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )

        return jsonify({'message': 'Password changed successfully'}), 200

    except Exception as e:
        return jsonify({'message': str(e)}), 500

# User management routes
@app.route('/api/users', methods=['GET'])
@jwt_required()
def get_users():
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)

        if current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        users = User.query.all()
        return jsonify({'users': [user.to_dict() for user in users]}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)

        if current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()

        # Update user fields
        if 'username' in data:
            user.username = data['username']
        if 'email' in data:
            user.email = data['email']
        if 'role' in data:
            user.role = data['role']
        if 'organization' in data:
            user.organization = data['organization']
        if 'is_active' in data:
            user.is_active = data['is_active']

        db.session.commit()

        # Log the update
        log_entry = AuditLog(
            user_id=current_user_id,
            action='USER_UPDATED',
            details=f'Updated user {user.username}',
            ip_address=request.remote_addr
        )
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'message': 'User updated successfully', 'user': user.to_dict()}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)

        if current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        if user_id == current_user_id:
            return jsonify({'error': 'Cannot delete your own account'}), 400

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        username = user.username
        db.session.delete(user)
        db.session.commit()

        # Log the deletion
        log_entry = AuditLog(
            user_id=current_user_id,
            action='USER_DELETED',
            details=f'Deleted user {username}',
            ip_address=request.remote_addr
        )
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'message': 'User deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Audit logs route
@app.route('/api/audit-logs', methods=['GET'])
@jwt_required()
def get_audit_logs():
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)

        if current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)

        logs = AuditLog.query.order_by(AuditLog.timestamp.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return jsonify({
            'logs': [log.to_dict() for log in logs.items],
            'total': logs.total,
            'pages': logs.pages,
            'current_page': page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Data management routes for Oracle tables

# Import Oracle DAO
try:
    from oracle_dao import EmployeurDAO, AssureDAO, BeneficiaireDAO, UserDAO
    oracle_available = True
except ImportError:
    oracle_available = False
    logger.warning("Oracle DAO not available, using mock data")

# Employeur routes
@app.route('/api/employeurs', methods=['GET'])
@jwt_required()
def get_employeurs():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')

        if app.config.get('DB_TYPE', '').lower() == 'oracle' and oracle_available:
            # Use Oracle DAO
            offset = (page - 1) * per_page
            employeurs = EmployeurDAO.get_all(search=search, limit=per_page, offset=offset)
            total_count = EmployeurDAO.count(search=search)
            total_pages = (total_count + per_page - 1) // per_page

            return jsonify({
                'employeurs': employeurs,
                'total': total_count,
                'pages': total_pages,
                'current_page': page
            }), 200
        else:
            # Use mock data for SQLite/development
            # Use mock data for SQLite/development
            mock_employeurs = [
                {
                    'id': 1,
                    'nom_entreprise': 'CNSS Maroc',
                    'numero_cnss': 'EMP001',
                    'adresse': '123 Rue Mohammed V, Casablanca',
                    'ville': 'Casablanca',
                    'telephone': '+212522123456',
                    'email': '<EMAIL>',
                    'secteur_activite': 'Administration Publique',
                    'statut': 'Actif',
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                },
                {
                    'id': 2,
                    'nom_entreprise': 'TechCorp Solutions',
                    'numero_cnss': 'EMP002',
                    'adresse': '456 Avenue Hassan II, Rabat',
                    'ville': 'Rabat',
                    'telephone': '+212537654321',
                    'email': '<EMAIL>',
                    'secteur_activite': 'Technologies',
                    'statut': 'Actif',
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }
            ]

            # Filter by search if provided
            if search:
                mock_employeurs = [emp for emp in mock_employeurs
                                 if search.lower() in emp['nom_entreprise'].lower()
                                 or search.lower() in emp['numero_cnss'].lower()]

            # Simple pagination for mock data
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            paginated_employeurs = mock_employeurs[start_idx:end_idx]

            return jsonify({
                'employeurs': paginated_employeurs,
                'total': len(mock_employeurs),
                'pages': (len(mock_employeurs) + per_page - 1) // per_page,
                'current_page': page
            }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/employeurs', methods=['POST'])
@jwt_required()
def create_employeur():
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)
        data = request.get_json()

        if app.config.get('DB_TYPE', '').lower() == 'oracle' and oracle_available:
            # Use Oracle DAO
            employeur_data = {
                'emp_mat': data['emp_mat'],
                'emp_cle': data['emp_cle'],
                'emp_rais': data['emp_rais'],
                'emp_sigle': data.get('emp_sigle', ''),
                'emp_activite': data.get('emp_activite', ''),
                'emp_email': data.get('emp_email', ''),
                'emp_tel': data.get('emp_tel', ''),
                'emp_dtaff': data.get('emp_dtaff'),
                'emp_didact': data.get('emp_didact'),
                'emp_adresse': data.get('emp_adresse', ''),
                'emp_ville': data.get('emp_ville', ''),
                'emp_code_postal': data.get('emp_code_postal', ''),
                'emp_statut': data.get('emp_statut', 'ACTIF')
            }

            new_id = EmployeurDAO.create(employeur_data)

            # Log the action
            log_user_action(
                user_id=current_user_id,
                username=current_user.username,
                action='EMPLOYEUR_CREATED',
                table_name='employeur',
                record_id=str(new_id),
                details=f'Created employeur {data["nom_entreprise"]}',
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )

            # Get the created employeur
            created_employeur = EmployeurDAO.get_by_id(new_id)
            return jsonify({'message': 'Employeur created successfully', 'employeur': created_employeur}), 201
        else:
            # Mock response for SQLite/development
            mock_employeur = {
                'id': 999,  # Mock ID
                'nom_entreprise': data['nom_entreprise'],
                'numero_cnss': data['numero_cnss'],
                'adresse': data.get('adresse', ''),
                'ville': data.get('ville', ''),
                'telephone': data.get('telephone', ''),
                'email': data.get('email', ''),
                'secteur_activite': data.get('secteur_activite', ''),
                'statut': data.get('statut', 'Actif'),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }

            return jsonify({'message': 'Employeur created successfully (mock)', 'employeur': mock_employeur}), 201

    except Exception as e:
        logger.error(f"Failed to create employeur: {e}")
        return jsonify({'error': str(e)}), 500

# Assure routes
@app.route('/api/assures', methods=['GET'])
@jwt_required()
def get_assures():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')

        query = Assure.query
        if search:
            query = query.filter(
                db.or_(
                    Assure.nom.contains(search),
                    Assure.prenom.contains(search),
                    Assure.numero_assure.contains(search),
                    Assure.cin.contains(search)
                )
            )

        assures = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'assures': [assure.to_dict() for assure in assures.items],
            'total': assures.total,
            'pages': assures.pages,
            'current_page': page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/assures', methods=['POST'])
@jwt_required()
def create_assure():
    try:
        current_user_id = get_jwt_identity()
        data = request.get_json()

        assure = Assure(
            numero_assure=data['numero_assure'],
            cin=data['cin'],
            nom=data['nom'],
            prenom=data['prenom'],
            date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date() if data.get('date_naissance') else None,
            lieu_naissance=data.get('lieu_naissance'),
            sexe=data.get('sexe'),
            adresse=data.get('adresse'),
            ville=data.get('ville'),
            telephone=data.get('telephone'),
            email=data.get('email'),
            employeur_id=data.get('employeur_id'),
            date_affiliation=datetime.strptime(data['date_affiliation'], '%Y-%m-%d').date() if data.get('date_affiliation') else None
        )

        db.session.add(assure)
        db.session.commit()

        # Log the action
        log_entry = AuditLog(
            user_id=current_user_id,
            action='ASSURE_CREATED',
            table_name='assure',
            record_id=str(assure.id),
            details=f'Created assure {assure.nom} {assure.prenom}',
            ip_address=request.remote_addr
        )
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'message': 'Assure created successfully', 'assure': assure.to_dict()}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Beneficiaire routes
@app.route('/api/beneficiaires', methods=['GET'])
@jwt_required()
def get_beneficiaires():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')

        query = Beneficiaire.query
        if search:
            query = query.filter(
                db.or_(
                    Beneficiaire.nom.contains(search),
                    Beneficiaire.prenom.contains(search),
                    Beneficiaire.numero_beneficiaire.contains(search)
                )
            )

        beneficiaires = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'beneficiaires': [ben.to_dict() for ben in beneficiaires.items],
            'total': beneficiaires.total,
            'pages': beneficiaires.pages,
            'current_page': page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Health check route
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()}), 200

def create_sample_data():
    """Create sample data for demonstration purposes"""

    # Check if sample data already exists
    if Employeur.query.first():
        return

    try:
        # Create sample employeurs
        employeur1 = Employeur(
            nom_entreprise='CNSS Maroc',
            numero_cnss='EMP001',
            adresse='123 Avenue Mohammed V',
            ville='Casablanca',
            telephone='+212522123456',
            email='<EMAIL>',
            secteur_activite='Administration Publique',
            nombre_employes=500,
            date_affiliation=date(2020, 1, 15)
        )

        employeur2 = Employeur(
            nom_entreprise='Tech Solutions SA',
            numero_cnss='EMP002',
            adresse='456 Boulevard Zerktouni',
            ville='Rabat',
            telephone='+212537654321',
            email='<EMAIL>',
            secteur_activite='Technologies',
            nombre_employes=150,
            date_affiliation=date(2021, 3, 10)
        )

        db.session.add_all([employeur1, employeur2])
        db.session.commit()

        # Create sample assures
        assure1 = Assure(
            numero_assure='ASS001',
            cin='AB123456',
            nom='Alami',
            prenom='Ahmed',
            date_naissance=date(1985, 5, 15),
            lieu_naissance='Casablanca',
            sexe='M',
            adresse='789 Rue des Fleurs',
            ville='Casablanca',
            telephone='+212661234567',
            email='<EMAIL>',
            employeur_id=employeur1.id,
            date_affiliation=date(2020, 2, 1)
        )

        assure2 = Assure(
            numero_assure='ASS002',
            cin='CD789012',
            nom='Benali',
            prenom='Fatima',
            date_naissance=date(1990, 8, 22),
            lieu_naissance='Rabat',
            sexe='F',
            adresse='321 Avenue Hassan II',
            ville='Rabat',
            telephone='+212662345678',
            email='<EMAIL>',
            employeur_id=employeur2.id,
            date_affiliation=date(2021, 4, 15)
        )

        db.session.add_all([assure1, assure2])
        db.session.commit()

        # Create sample beneficiaires
        beneficiaire1 = Beneficiaire(
            numero_beneficiaire='BEN001',
            assure_id=assure1.id,
            nom='Alami',
            prenom='Aicha',
            date_naissance=date(1987, 3, 10),
            sexe='F',
            relation_assure='conjoint',
            adresse='789 Rue des Fleurs',
            ville='Casablanca',
            telephone='+212661234568'
        )

        beneficiaire2 = Beneficiaire(
            numero_beneficiaire='BEN002',
            assure_id=assure1.id,
            nom='Alami',
            prenom='Youssef',
            date_naissance=date(2010, 12, 5),
            sexe='M',
            relation_assure='enfant',
            adresse='789 Rue des Fleurs',
            ville='Casablanca'
        )

        db.session.add_all([beneficiaire1, beneficiaire2])
        db.session.commit()

        print("Sample data created successfully")

    except Exception as e:
        db.session.rollback()
        print(f"Error creating sample data: {e}")

# Initialize database
def create_tables():
    with app.app_context():
        # Only create tables if using SQLite, Oracle tables already exist
        if app.config.get('DB_TYPE', '').lower() != 'oracle':
            db.create_all()

            # Create default admin user if it doesn't exist (SQLite only)
            admin_user = User.query.filter_by(email='<EMAIL>').first()
            if not admin_user:
                admin_user = User(
                    email='<EMAIL>',
                    first_name='Admin',
                    last_name='User',
                    role='admin'
                )
                admin_user.set_password('admin123')  # Change this in production
                db.session.add(admin_user)
                db.session.commit()
                print("Default admin user created: <EMAIL>/admin123")

            # Create default agent user if it doesn't exist (SQLite only)
            agent_user = User.query.filter_by(email='<EMAIL>').first()
            if not agent_user:
                agent_user = User(
                    email='<EMAIL>',
                    first_name='Agent',
                    last_name='User',
                    role='agent'
                )
                agent_user.set_password('agent123')  # Change this in production
                db.session.add(agent_user)
                db.session.commit()
                print("Default agent user created: <EMAIL>/agent123")

            # Create sample data for demonstration only if using SQLite
            create_sample_data()
        else:
            print("Using Oracle database - tables and users should already exist")
            # Check if any users exist
            user_count = User.query.count()
            print(f"Found {user_count} users in USERS table")

if __name__ == '__main__':
    create_tables()  # Initialize database on startup
    app.run(debug=True, host='0.0.0.0', port=5000)
