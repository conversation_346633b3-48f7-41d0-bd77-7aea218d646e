from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON><PERSON><PERSON>, jwt_required, create_access_token, get_jwt_identity
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta, date
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key-change-this')
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-string-change-this')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# For now, we'll use SQLite for user management, Oracle for business data
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL', 'sqlite:///cnss_users.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Oracle configuration (will be used later)
app.config['ORACLE_USER'] = os.getenv('ORACLE_USER', 'your_oracle_user')
app.config['ORACLE_PASSWORD'] = os.getenv('ORACLE_PASSWORD', 'your_oracle_password')
app.config['ORACLE_DSN'] = os.getenv('ORACLE_DSN', 'localhost:1521/XE')

# Initialize extensions
db = SQLAlchemy(app)
jwt = JWTManager(app)
CORS(app)

# Database Models
# User model for authentication
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='agent')  # 'agent' or 'admin'
    organization = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'organization': self.organization,
            'created_at': self.created_at.isoformat(),
            'is_active': self.is_active
        }

# Audit log model
class AuditLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)
    table_name = db.Column(db.String(50))
    record_id = db.Column(db.String(50))
    details = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(45))

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'action': self.action,
            'table_name': self.table_name,
            'record_id': self.record_id,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'ip_address': self.ip_address
        }

# Oracle database models for business data
class Employeur(db.Model):
    __tablename__ = 'employeur'
    id = db.Column(db.Integer, primary_key=True)
    nom_entreprise = db.Column(db.String(200), nullable=False)
    numero_cnss = db.Column(db.String(50), unique=True, nullable=False)
    adresse = db.Column(db.String(500))
    ville = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    secteur_activite = db.Column(db.String(100))
    nombre_employes = db.Column(db.Integer)
    date_affiliation = db.Column(db.Date)
    statut = db.Column(db.String(20), default='actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'nom_entreprise': self.nom_entreprise,
            'numero_cnss': self.numero_cnss,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'email': self.email,
            'secteur_activite': self.secteur_activite,
            'nombre_employes': self.nombre_employes,
            'date_affiliation': self.date_affiliation.isoformat() if self.date_affiliation else None,
            'statut': self.statut,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Assure(db.Model):
    __tablename__ = 'assure'
    id = db.Column(db.Integer, primary_key=True)
    numero_assure = db.Column(db.String(50), unique=True, nullable=False)
    cin = db.Column(db.String(20), unique=True, nullable=False)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    date_naissance = db.Column(db.Date)
    lieu_naissance = db.Column(db.String(100))
    sexe = db.Column(db.String(1))
    adresse = db.Column(db.String(500))
    ville = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    employeur_id = db.Column(db.Integer, db.ForeignKey('employeur.id'))
    date_affiliation = db.Column(db.Date)
    statut = db.Column(db.String(20), default='actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    employeur = db.relationship('Employeur', backref=db.backref('assures', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'numero_assure': self.numero_assure,
            'cin': self.cin,
            'nom': self.nom,
            'prenom': self.prenom,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'lieu_naissance': self.lieu_naissance,
            'sexe': self.sexe,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'email': self.email,
            'employeur_id': self.employeur_id,
            'employeur_nom': self.employeur.nom_entreprise if self.employeur else None,
            'date_affiliation': self.date_affiliation.isoformat() if self.date_affiliation else None,
            'statut': self.statut,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Beneficiaire(db.Model):
    __tablename__ = 'beneficiaire'
    id = db.Column(db.Integer, primary_key=True)
    numero_beneficiaire = db.Column(db.String(50), unique=True, nullable=False)
    assure_id = db.Column(db.Integer, db.ForeignKey('assure.id'), nullable=False)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    date_naissance = db.Column(db.Date)
    sexe = db.Column(db.String(1))
    relation_assure = db.Column(db.String(50))  # conjoint, enfant, parent, etc.
    adresse = db.Column(db.String(500))
    ville = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    statut = db.Column(db.String(20), default='actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    assure = db.relationship('Assure', backref=db.backref('beneficiaires', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'numero_beneficiaire': self.numero_beneficiaire,
            'assure_id': self.assure_id,
            'assure_nom': f"{self.assure.nom} {self.assure.prenom}" if self.assure else None,
            'nom': self.nom,
            'prenom': self.prenom,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'sexe': self.sexe,
            'relation_assure': self.relation_assure,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'statut': self.statut,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

# Authentication routes
@app.route('/api/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': 'Username and password are required'}), 400

        user = User.query.filter_by(username=username, is_active=True).first()

        if user and user.check_password(password):
            access_token = create_access_token(
                identity=user.id,
                additional_claims={
                    'username': user.username,
                    'role': user.role,
                    'organization': user.organization
                }
            )
            
            # Log the login
            log_entry = AuditLog(
                user_id=user.id,
                action='LOGIN',
                details=f'User {user.username} logged in',
                ip_address=request.remote_addr
            )
            db.session.add(log_entry)
            db.session.commit()

            return jsonify({
                'access_token': access_token,
                'user': user.to_dict()
            }), 200
        else:
            return jsonify({'error': 'Invalid credentials'}), 401

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/register', methods=['POST'])
@jwt_required()
def register():
    try:
        # Only admins can register new users
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)
        
        if current_user.role != 'admin':
            return jsonify({'error': 'Only admins can register new users'}), 403

        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        role = data.get('role', 'agent')
        organization = data.get('organization')

        if not all([username, email, password, organization]):
            return jsonify({'error': 'All fields are required'}), 400

        # Check if user already exists
        if User.query.filter_by(username=username).first():
            return jsonify({'error': 'Username already exists'}), 400

        if User.query.filter_by(email=email).first():
            return jsonify({'error': 'Email already exists'}), 400

        # Create new user
        new_user = User(
            username=username,
            email=email,
            role=role,
            organization=organization
        )
        new_user.set_password(password)

        db.session.add(new_user)
        db.session.commit()

        # Log the registration
        log_entry = AuditLog(
            user_id=current_user_id,
            action='USER_CREATED',
            details=f'Created user {username} with role {role}',
            ip_address=request.remote_addr
        )
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'message': 'User created successfully', 'user': new_user.to_dict()}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/me', methods=['GET'])
@jwt_required()
def get_current_user():
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify({'user': user.to_dict()}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# User management routes
@app.route('/api/users', methods=['GET'])
@jwt_required()
def get_users():
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)

        if current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        users = User.query.all()
        return jsonify({'users': [user.to_dict() for user in users]}), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>', methods=['PUT'])
@jwt_required()
def update_user(user_id):
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)

        if current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        data = request.get_json()

        # Update user fields
        if 'username' in data:
            user.username = data['username']
        if 'email' in data:
            user.email = data['email']
        if 'role' in data:
            user.role = data['role']
        if 'organization' in data:
            user.organization = data['organization']
        if 'is_active' in data:
            user.is_active = data['is_active']

        db.session.commit()

        # Log the update
        log_entry = AuditLog(
            user_id=current_user_id,
            action='USER_UPDATED',
            details=f'Updated user {user.username}',
            ip_address=request.remote_addr
        )
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'message': 'User updated successfully', 'user': user.to_dict()}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@jwt_required()
def delete_user(user_id):
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)

        if current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        if user_id == current_user_id:
            return jsonify({'error': 'Cannot delete your own account'}), 400

        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        username = user.username
        db.session.delete(user)
        db.session.commit()

        # Log the deletion
        log_entry = AuditLog(
            user_id=current_user_id,
            action='USER_DELETED',
            details=f'Deleted user {username}',
            ip_address=request.remote_addr
        )
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'message': 'User deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Audit logs route
@app.route('/api/audit-logs', methods=['GET'])
@jwt_required()
def get_audit_logs():
    try:
        current_user_id = get_jwt_identity()
        current_user = User.query.get(current_user_id)

        if current_user.role != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)

        logs = AuditLog.query.order_by(AuditLog.timestamp.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return jsonify({
            'logs': [log.to_dict() for log in logs.items],
            'total': logs.total,
            'pages': logs.pages,
            'current_page': page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Data management routes for Oracle tables

# Employeur routes
@app.route('/api/employeurs', methods=['GET'])
@jwt_required()
def get_employeurs():
    try:
        from models import Employeur
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')

        query = Employeur.query
        if search:
            query = query.filter(
                db.or_(
                    Employeur.nom_entreprise.contains(search),
                    Employeur.numero_cnss.contains(search),
                    Employeur.ville.contains(search)
                )
            )

        employeurs = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'employeurs': [emp.to_dict() for emp in employeurs.items],
            'total': employeurs.total,
            'pages': employeurs.pages,
            'current_page': page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/employeurs', methods=['POST'])
@jwt_required()
def create_employeur():
    try:
        from models import Employeur
        current_user_id = get_jwt_identity()
        data = request.get_json()

        employeur = Employeur(
            nom_entreprise=data['nom_entreprise'],
            numero_cnss=data['numero_cnss'],
            adresse=data.get('adresse'),
            ville=data.get('ville'),
            telephone=data.get('telephone'),
            email=data.get('email'),
            secteur_activite=data.get('secteur_activite'),
            nombre_employes=data.get('nombre_employes'),
            date_affiliation=datetime.strptime(data['date_affiliation'], '%Y-%m-%d').date() if data.get('date_affiliation') else None
        )

        db.session.add(employeur)
        db.session.commit()

        # Log the action
        log_entry = AuditLog(
            user_id=current_user_id,
            action='EMPLOYEUR_CREATED',
            table_name='employeur',
            record_id=str(employeur.id),
            details=f'Created employeur {employeur.nom_entreprise}',
            ip_address=request.remote_addr
        )
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'message': 'Employeur created successfully', 'employeur': employeur.to_dict()}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Assure routes
@app.route('/api/assures', methods=['GET'])
@jwt_required()
def get_assures():
    try:
        from models import Assure
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')

        query = Assure.query
        if search:
            query = query.filter(
                db.or_(
                    Assure.nom.contains(search),
                    Assure.prenom.contains(search),
                    Assure.numero_assure.contains(search),
                    Assure.cin.contains(search)
                )
            )

        assures = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'assures': [assure.to_dict() for assure in assures.items],
            'total': assures.total,
            'pages': assures.pages,
            'current_page': page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/assures', methods=['POST'])
@jwt_required()
def create_assure():
    try:
        from models import Assure
        current_user_id = get_jwt_identity()
        data = request.get_json()

        assure = Assure(
            numero_assure=data['numero_assure'],
            cin=data['cin'],
            nom=data['nom'],
            prenom=data['prenom'],
            date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date() if data.get('date_naissance') else None,
            lieu_naissance=data.get('lieu_naissance'),
            sexe=data.get('sexe'),
            adresse=data.get('adresse'),
            ville=data.get('ville'),
            telephone=data.get('telephone'),
            email=data.get('email'),
            employeur_id=data.get('employeur_id'),
            date_affiliation=datetime.strptime(data['date_affiliation'], '%Y-%m-%d').date() if data.get('date_affiliation') else None
        )

        db.session.add(assure)
        db.session.commit()

        # Log the action
        log_entry = AuditLog(
            user_id=current_user_id,
            action='ASSURE_CREATED',
            table_name='assure',
            record_id=str(assure.id),
            details=f'Created assure {assure.nom} {assure.prenom}',
            ip_address=request.remote_addr
        )
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'message': 'Assure created successfully', 'assure': assure.to_dict()}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Beneficiaire routes
@app.route('/api/beneficiaires', methods=['GET'])
@jwt_required()
def get_beneficiaires():
    try:
        from models import Beneficiaire
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')

        query = Beneficiaire.query
        if search:
            query = query.filter(
                db.or_(
                    Beneficiaire.nom.contains(search),
                    Beneficiaire.prenom.contains(search),
                    Beneficiaire.numero_beneficiaire.contains(search)
                )
            )

        beneficiaires = query.paginate(page=page, per_page=per_page, error_out=False)

        return jsonify({
            'beneficiaires': [ben.to_dict() for ben in beneficiaires.items],
            'total': beneficiaires.total,
            'pages': beneficiaires.pages,
            'current_page': page
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Health check route
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()}), 200

def create_sample_data():
    """Create sample data for demonstration purposes"""
    from models import Employeur, Assure, Beneficiaire
    from datetime import date

    # Check if sample data already exists
    if Employeur.query.first():
        return

    try:
        # Create sample employeurs
        employeur1 = Employeur(
            nom_entreprise='CNSS Maroc',
            numero_cnss='EMP001',
            adresse='123 Avenue Mohammed V',
            ville='Casablanca',
            telephone='+212522123456',
            email='<EMAIL>',
            secteur_activite='Administration Publique',
            nombre_employes=500,
            date_affiliation=date(2020, 1, 15)
        )

        employeur2 = Employeur(
            nom_entreprise='Tech Solutions SA',
            numero_cnss='EMP002',
            adresse='456 Boulevard Zerktouni',
            ville='Rabat',
            telephone='+212537654321',
            email='<EMAIL>',
            secteur_activite='Technologies',
            nombre_employes=150,
            date_affiliation=date(2021, 3, 10)
        )

        db.session.add_all([employeur1, employeur2])
        db.session.commit()

        # Create sample assures
        assure1 = Assure(
            numero_assure='ASS001',
            cin='AB123456',
            nom='Alami',
            prenom='Ahmed',
            date_naissance=date(1985, 5, 15),
            lieu_naissance='Casablanca',
            sexe='M',
            adresse='789 Rue des Fleurs',
            ville='Casablanca',
            telephone='+212661234567',
            email='<EMAIL>',
            employeur_id=employeur1.id,
            date_affiliation=date(2020, 2, 1)
        )

        assure2 = Assure(
            numero_assure='ASS002',
            cin='CD789012',
            nom='Benali',
            prenom='Fatima',
            date_naissance=date(1990, 8, 22),
            lieu_naissance='Rabat',
            sexe='F',
            adresse='321 Avenue Hassan II',
            ville='Rabat',
            telephone='+212662345678',
            email='<EMAIL>',
            employeur_id=employeur2.id,
            date_affiliation=date(2021, 4, 15)
        )

        db.session.add_all([assure1, assure2])
        db.session.commit()

        # Create sample beneficiaires
        beneficiaire1 = Beneficiaire(
            numero_beneficiaire='BEN001',
            assure_id=assure1.id,
            nom='Alami',
            prenom='Aicha',
            date_naissance=date(1987, 3, 10),
            sexe='F',
            relation_assure='conjoint',
            adresse='789 Rue des Fleurs',
            ville='Casablanca',
            telephone='+212661234568'
        )

        beneficiaire2 = Beneficiaire(
            numero_beneficiaire='BEN002',
            assure_id=assure1.id,
            nom='Alami',
            prenom='Youssef',
            date_naissance=date(2010, 12, 5),
            sexe='M',
            relation_assure='enfant',
            adresse='789 Rue des Fleurs',
            ville='Casablanca'
        )

        db.session.add_all([beneficiaire1, beneficiaire2])
        db.session.commit()

        print("Sample data created successfully")

    except Exception as e:
        db.session.rollback()
        print(f"Error creating sample data: {e}")

# Initialize database
def create_tables():
    with app.app_context():
        db.create_all()

        # Create default admin user if it doesn't exist
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin',
                organization='CNSS'
            )
            admin_user.set_password('admin123')  # Change this in production
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created: admin/admin123")

        # Create sample data for demonstration
        create_sample_data()

if __name__ == '__main__':
    create_tables()  # Initialize database on startup
    app.run(debug=True, host='0.0.0.0', port=5000)
