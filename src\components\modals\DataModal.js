import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Save, User, Building, Users } from "lucide-react";

const DataModal = ({ isOpen, onClose, onSave, data, type, mode = "add" }) => {
  const [formData, setFormData] = useState({});
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (isOpen) {
      if (mode === "edit" && data) {
        setFormData(data);
      } else {
        setFormData(getInitialFormData(type));
      }
      setErrors({});
    }
  }, [isOpen, data, type, mode]);

  const getInitialFormData = (type) => {
    switch (type) {
      case "employeurs":
        return {
          emp_mat: "",
          emp_cle: "",
          emp_rais: "",
          emp_sigle: "",
          emp_activite: "",
          emp_email: "",
          emp_tel: "",
          emp_dtaff: "",
          emp_didact: "",
          emp_adresse: "",
          emp_ville: "",
          emp_code_postal: "",
          emp_statut: "ACTIF",
        };
      case "assures":
        return {
          ass_mat: "",
          ass_cle: "",
          emp_mat: "",
          emp_cle: "",
          ass_nom: "",
          ass_prenom: "",
          ass_dteff: "",
          ass_dtimat: "",
          ass_rib: "",
          ass_chrefs: "",
          ass_cin: "",
          ass_dtnaiss: "",
          ass_sexe: "",
          ass_tel: "",
          ass_email: "",
          etat_engagement: "ACTIF",
        };
      case "beneficiaires":
        return {
          ben_mat: "",
          ben_cle: "",
          ben_idocnss: "",
          ben_nom: "",
          ben_prenom: "",
          ben_nom_ar: "",
          ben_prn_ar: "",
          ben_email: "",
          ben_tel: "",
          ben_numid: "",
          ben_typid: "",
          ben_dtnais: "",
          ben_dtdeces: "",
          ben_sexe: "",
          ben_type: "",
          ben_statut: "ACTIF",
        };
      default:
        return {};
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    switch (type) {
      case "employeurs":
        if (!formData.nom_entreprise)
          newErrors.nom_entreprise = "Company name is required";
        if (!formData.numero_cnss)
          newErrors.numero_cnss = "CNSS number is required";
        break;
      case "assures":
        if (!formData.nom) newErrors.nom = "Last name is required";
        if (!formData.prenom) newErrors.prenom = "First name is required";
        if (!formData.cin) newErrors.cin = "CIN is required";
        if (!formData.numero_assure)
          newErrors.numero_assure = "Insurance number is required";
        break;
      case "beneficiaires":
        if (!formData.nom) newErrors.nom = "Last name is required";
        if (!formData.prenom) newErrors.prenom = "First name is required";
        if (!formData.relation_assure)
          newErrors.relation_assure = "Relationship is required";
        if (!formData.assure_id)
          newErrors.assure_id = "Insured person is required";
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error("Failed to save:", error);
      setErrors({ submit: error.message || "Failed to save record" });
    } finally {
      setLoading(false);
    }
  };

  const getModalTitle = () => {
    const typeNames = {
      employeurs: "Employer",
      assures: "Insured Person",
      beneficiaires: "Beneficiary",
    };
    return `${mode === "edit" ? "Edit" : "Add New"} ${
      typeNames[type] || "Record"
    }`;
  };

  const getModalIcon = () => {
    switch (type) {
      case "employeurs":
        return Building;
      case "assures":
        return User;
      case "beneficiaires":
        return Users;
      default:
        return User;
    }
  };

  const ModalIcon = getModalIcon();

  const renderFormFields = () => {
    switch (type) {
      case "employeurs":
        return (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Matricule Employeur *
                </label>
                <input
                  type="text"
                  name="emp_mat"
                  value={formData.emp_mat || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.emp_mat ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter employer matricule"
                />
                {errors.emp_mat && (
                  <p className="text-red-500 text-xs mt-1">{errors.emp_mat}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Clé Employeur *
                </label>
                <input
                  type="text"
                  name="emp_cle"
                  value={formData.emp_cle || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.emp_cle ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter employer key"
                />
                {errors.emp_cle && (
                  <p className="text-red-500 text-xs mt-1">{errors.emp_cle}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Raison Sociale *
                </label>
                <input
                  type="text"
                  name="emp_rais"
                  value={formData.emp_rais || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.emp_rais ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter company name"
                />
                {errors.emp_rais && (
                  <p className="text-red-500 text-xs mt-1">{errors.emp_rais}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Sigle
                </label>
                <input
                  type="text"
                  name="emp_sigle"
                  value={formData.emp_sigle || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.emp_sigle ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter company acronym"
                />
                {errors.emp_sigle && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.emp_sigle}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Activité
                </label>
                <input
                  type="text"
                  name="emp_activite"
                  value={formData.emp_activite || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter business activity"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  name="emp_email"
                  value={formData.emp_email || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter email address"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Téléphone
                </label>
                <input
                  type="tel"
                  name="emp_tel"
                  value={formData.emp_tel || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter phone number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date d'Affiliation
                </label>
                <input
                  type="date"
                  name="emp_dtaff"
                  value={formData.emp_dtaff || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date d'Inactivité
                </label>
                <input
                  type="date"
                  name="emp_didact"
                  value={formData.emp_didact || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ville
                </label>
                <input
                  type="text"
                  name="emp_ville"
                  value={formData.emp_ville || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter city"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Code Postal
                </label>
                <input
                  type="text"
                  name="emp_code_postal"
                  value={formData.emp_code_postal || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter postal code"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Statut
                </label>
                <select
                  name="emp_statut"
                  value={formData.emp_statut || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select status</option>
                  <option value="ACTIF">ACTIF</option>
                  <option value="INACTIF">INACTIF</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Adresse
              </label>
              <textarea
                name="emp_adresse"
                value={formData.emp_adresse || ""}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter company address"
              />
            </div>
          </>
        );

      case "assures":
        return (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Matricule Assuré *
                </label>
                <input
                  type="text"
                  name="ass_mat"
                  value={formData.ass_mat || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.ass_mat ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter matricule"
                />
                {errors.ass_mat && (
                  <p className="text-red-500 text-xs mt-1">{errors.ass_mat}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Clé Assuré *
                </label>
                <input
                  type="text"
                  name="ass_cle"
                  value={formData.ass_cle || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.ass_cle ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter key"
                />
                {errors.ass_cle && (
                  <p className="text-red-500 text-xs mt-1">{errors.ass_cle}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Matricule Employeur *
                </label>
                <input
                  type="text"
                  name="emp_mat"
                  value={formData.emp_mat || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.emp_mat ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter employer matricule"
                />
                {errors.emp_mat && (
                  <p className="text-red-500 text-xs mt-1">{errors.emp_mat}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Clé Employeur *
                </label>
                <input
                  type="text"
                  name="emp_cle"
                  value={formData.emp_cle || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.emp_cle ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter employer key"
                />
                {errors.emp_cle && (
                  <p className="text-red-500 text-xs mt-1">{errors.emp_cle}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom *
                </label>
                <input
                  type="text"
                  name="ass_nom"
                  value={formData.ass_nom || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.ass_nom ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter last name"
                />
                {errors.ass_nom && (
                  <p className="text-red-500 text-xs mt-1">{errors.ass_nom}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prénom *
                </label>
                <input
                  type="text"
                  name="ass_prenom"
                  value={formData.ass_prenom || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.ass_prenom ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter first name"
                />
                {errors.ass_prenom && (
                  <p className="text-red-500 text-xs mt-1">{errors.ass_prenom}</p>
                )}
              </div>
            </div>
          </>
        );

      case "beneficiaires":
        return (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Matricule Bénéficiaire *
                </label>
                <input
                  type="text"
                  name="ben_mat"
                  value={formData.ben_mat || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.ben_mat ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter matricule"
                />
                {errors.ben_mat && (
                  <p className="text-red-500 text-xs mt-1">{errors.ben_mat}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Clé Bénéficiaire *
                </label>
                <input
                  type="text"
                  name="ben_cle"
                  value={formData.ben_cle || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.ben_cle ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter key"
                />
                {errors.ben_cle && (
                  <p className="text-red-500 text-xs mt-1">{errors.ben_cle}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom *
                </label>
                <input
                  type="text"
                  name="ben_nom"
                  value={formData.ben_nom || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.ben_nom ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter last name"
                />
                {errors.ben_nom && (
                  <p className="text-red-500 text-xs mt-1">{errors.ben_nom}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prénom *
                </label>
                <input
                  type="text"
                  name="ben_prenom"
                  value={formData.ben_prenom || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.ben_prenom ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter first name"
                />
                {errors.ben_prenom && (
                  <p className="text-red-500 text-xs mt-1">{errors.ben_prenom}</p>
                )}
              </div>
            </div>
          </>
        );

      default:
        return null;
    }
  };

  const getModalTitle = () => {
    const action = mode === "edit" ? "Edit" : "Add";
    switch (type) {
      case "employeurs":
        return `${action} Employeur`;
      case "assures":
        return `${action} Assuré`;
      case "beneficiaires":
        return `${action} Bénéficiaire`;
      default:
        return `${action} Record`;
    }
  };

  const getModalIcon = () => {
    switch (type) {
      case "employeurs":
        return Building;
      case "assures":
        return User;
      case "beneficiaires":
        return Users;
      default:
        return User;
    }
  };

  const validateForm = () => {
    const newErrors = {};

    switch (type) {
      case "employeurs":
        if (!formData.emp_mat?.trim()) {
          newErrors.emp_mat = "Matricule employeur is required";
        }
        if (!formData.emp_cle?.trim()) {
          newErrors.emp_cle = "Clé employeur is required";
        }
        if (!formData.emp_rais?.trim()) {
          newErrors.emp_rais = "Raison sociale is required";
        }
        break;

      case "assures":
        if (!formData.ass_mat?.trim()) {
          newErrors.ass_mat = "Matricule assuré is required";
        }
        if (!formData.ass_cle?.trim()) {
          newErrors.ass_cle = "Clé assuré is required";
        }
        if (!formData.emp_mat?.trim()) {
          newErrors.emp_mat = "Matricule employeur is required";
        }
        if (!formData.emp_cle?.trim()) {
          newErrors.emp_cle = "Clé employeur is required";
        }
        if (!formData.ass_nom?.trim()) {
          newErrors.ass_nom = "Nom is required";
        }
        if (!formData.ass_prenom?.trim()) {
          newErrors.ass_prenom = "Prénom is required";
        }
        break;

      case "beneficiaires":
        if (!formData.ben_mat?.trim()) {
          newErrors.ben_mat = "Matricule bénéficiaire is required";
        }
        if (!formData.ben_cle?.trim()) {
          newErrors.ben_cle = "Clé bénéficiaire is required";
        }
        if (!formData.ben_nom?.trim()) {
          newErrors.ben_nom = "Nom is required";
        }
        if (!formData.ben_prenom?.trim()) {
          newErrors.ben_prenom = "Prénom is required";
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      setErrors({
        submit: error.message || "An error occurred while saving",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const ModalIcon = getModalIcon();

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                  <ModalIcon className="w-6 h-6 text-white" />
                </div>
                <h2 className="text-xl font-bold text-white">
                  {getModalTitle()}
                </h2>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="px-6 py-4">
            <div className="space-y-4 max-h-[60vh] overflow-y-auto">
              {renderFormFields()}

              {errors.submit && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800 text-sm">{errors.submit}</p>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>{loading ? "Saving..." : "Save"}</span>
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default DataModal;
                name="statut"
                value={formData.statut || "Actif"}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Actif">Active</option>
                <option value="Inactif">Inactive</option>
                <option value="Suspendu">Suspended</option>
              </select>
            </div>
          </>
        );

      case "assures":
        return (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name *
                </label>
                <input
                  type="text"
                  name="nom"
                  value={formData.nom || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.nom ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter last name"
                />
                {errors.nom && (
                  <p className="text-red-500 text-xs mt-1">{errors.nom}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <input
                  type="text"
                  name="prenom"
                  value={formData.prenom || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.prenom ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter first name"
                />
                {errors.prenom && (
                  <p className="text-red-500 text-xs mt-1">{errors.prenom}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  CIN *
                </label>
                <input
                  type="text"
                  name="cin"
                  value={formData.cin || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.cin ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter CIN"
                />
                {errors.cin && (
                  <p className="text-red-500 text-xs mt-1">{errors.cin}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Insurance Number *
                </label>
                <input
                  type="text"
                  name="numero_assure"
                  value={formData.numero_assure || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.numero_assure ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter insurance number"
                />
                {errors.numero_assure && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.numero_assure}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date of Birth
                </label>
                <input
                  type="date"
                  name="date_naissance"
                  value={formData.date_naissance || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Place of Birth
                </label>
                <input
                  type="text"
                  name="lieu_naissance"
                  value={formData.lieu_naissance || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter place of birth"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Address
              </label>
              <textarea
                name="adresse"
                value={formData.adresse || ""}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter address"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <input
                  type="tel"
                  name="telephone"
                  value={formData.telephone || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter phone number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter email address"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                name="statut"
                value={formData.statut || "Actif"}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Actif">Active</option>
                <option value="Inactif">Inactive</option>
                <option value="Suspendu">Suspended</option>
              </select>
            </div>
          </>
        );

      case "beneficiaires":
        return (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name *
                </label>
                <input
                  type="text"
                  name="nom"
                  value={formData.nom || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.nom ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter last name"
                />
                {errors.nom && (
                  <p className="text-red-500 text-xs mt-1">{errors.nom}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <input
                  type="text"
                  name="prenom"
                  value={formData.prenom || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.prenom ? "border-red-500" : "border-gray-300"
                  }`}
                  placeholder="Enter first name"
                />
                {errors.prenom && (
                  <p className="text-red-500 text-xs mt-1">{errors.prenom}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  CIN
                </label>
                <input
                  type="text"
                  name="cin"
                  value={formData.cin || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter CIN"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date of Birth
                </label>
                <input
                  type="date"
                  name="date_naissance"
                  value={formData.date_naissance || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Relationship *
                </label>
                <select
                  name="relation_assure"
                  value={formData.relation_assure || ""}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.relation_assure
                      ? "border-red-500"
                      : "border-gray-300"
                  }`}
                >
                  <option value="">Select relationship</option>
                  <option value="Époux">Spouse</option>
                  <option value="Épouse">Spouse</option>
                  <option value="Enfant">Child</option>
                  <option value="Parent">Parent</option>
                  <option value="Autre">Other</option>
                </select>
                {errors.relation_assure && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.relation_assure}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Beneficiary Number
                </label>
                <input
                  type="text"
                  name="numero_beneficiaire"
                  value={formData.numero_beneficiaire || ""}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter beneficiary number"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                name="statut"
                value={formData.statut || "Actif"}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Actif">Active</option>
                <option value="Inactif">Inactive</option>
                <option value="Suspendu">Suspended</option>
              </select>
            </div>
          </>
        );

      default:
        return <div>Form fields for {type} will be implemented</div>;
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-[9999] overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 transition-opacity z-[9998]"
              onClick={onClose}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="relative inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full z-[9999]"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="bg-white px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <ModalIcon className="w-5 h-5 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900">
                      {getModalTitle()}
                    </h3>
                  </div>
                  <button
                    onClick={onClose}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 text-gray-400" />
                  </button>
                </div>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="px-6 py-4">
                <div className="space-y-4">
                  {renderFormFields()}

                  {errors.submit && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-red-800 text-sm">{errors.submit}</p>
                    </div>
                  )}
                </div>

                {/* Footer */}
                <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <motion.button
                    type="submit"
                    disabled={loading}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`flex items-center px-4 py-2 text-white rounded-lg transition-colors ${
                      loading
                        ? "bg-gray-400 cursor-not-allowed"
                        : "bg-blue-600 hover:bg-blue-700"
                    }`}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {loading ? "Saving..." : "Save"}
                  </motion.button>
                </div>
              </form>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default DataModal;
