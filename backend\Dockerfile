# Use Python 3.9 slim image
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    unixodbc-dev \
    curl \
    libaio1 \
    && rm -rf /var/lib/apt/lists/*

# Install Oracle Instant Client (for cx_Oracle)
RUN mkdir -p /opt/oracle && \
    cd /opt/oracle && \
    curl -o instantclient-basiclite-linux.x64-********.0.zip \
    https://download.oracle.com/otn_software/linux/instantclient/211000/instantclient-basiclite-linux.x64-********.0.zip && \
    unzip instantclient-basiclite-linux.x64-********.0.zip && \
    rm instantclient-basiclite-linux.x64-********.0.zip && \
    cd instantclient_21_1 && \
    echo /opt/oracle/instantclient_21_1 > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

# Set Oracle environment variables
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_21_1:$LD_LIBRARY_PATH
ENV PATH=/opt/oracle/instantclient_21_1:$PATH

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create directory for SQLite database
RUN mkdir -p /app/data

# Expose port
EXPOSE 5000

# Set environment variables
ENV FLASK_APP=app.py
ENV FLASK_ENV=production
ENV PYTHONPATH=/app

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# Run the application
CMD ["python", "app.py"]
