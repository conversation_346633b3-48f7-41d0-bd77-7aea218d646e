import os
import oracledb
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

class OracleConfig:
    """Oracle Database Configuration"""
    
    def __init__(self):
        # Oracle connection parameters
        self.host = os.getenv('ORACLE_HOST', 'localhost')
        self.port = os.getenv('ORACLE_PORT', '1521')
        self.service_name = os.getenv('ORACLE_SERVICE_NAME', 'XE')
        self.username = os.getenv('ORACLE_USERNAME', 'hr')
        self.password = os.getenv('ORACLE_PASSWORD', 'password')
        
        # Connection string for SQLAlchemy
        self.connection_string = f"oracle+oracledb://{self.username}:{self.password}@{self.host}:{self.port}/{self.service_name}"
        
    def get_engine(self):
        """Create SQLAlchemy engine for Oracle"""
        try:
            engine = create_engine(
                self.connection_string,
                echo=True,  # Set to False in production
                pool_pre_ping=True,
                pool_recycle=3600
            )
            return engine
        except Exception as e:
            print(f"Error creating Oracle engine: {e}")
            return None
    
    def test_connection(self):
        """Test Oracle database connection"""
        try:
            engine = self.get_engine()
            if engine:
                with engine.connect() as conn:
                    result = conn.execute("SELECT 1 FROM DUAL")
                    print("✅ Oracle connection successful!")
                    return True
        except Exception as e:
            print(f"❌ Oracle connection failed: {e}")
            return False
    
    def get_session(self):
        """Get database session"""
        engine = self.get_engine()
        if engine:
            Session = sessionmaker(bind=engine)
            return Session()
        return None

# Global Oracle config instance
oracle_config = OracleConfig()
