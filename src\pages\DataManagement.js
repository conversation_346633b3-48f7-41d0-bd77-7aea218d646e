import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Database, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Eye,
  Building,
  Users,
  UserCheck,
  Filter,
  Download,
  Upload
} from 'lucide-react';
import DashboardLayout from '../components/layout/DashboardLayout';
import { dataAPI } from '../utils/api';

const DataCard = ({ title, count, icon: Icon, color, onClick, isActive }) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={onClick}
    className={`cursor-pointer rounded-xl p-6 border-2 transition-all ${
      isActive 
        ? `${color} border-blue-500 shadow-lg` 
        : 'bg-white border-gray-200 hover:border-gray-300 shadow-sm'
    }`}
  >
    <div className="flex items-center justify-between">
      <div>
        <p className={`text-sm font-medium ${isActive ? 'text-white' : 'text-gray-600'}`}>
          {title}
        </p>
        <p className={`text-3xl font-bold mt-2 ${isActive ? 'text-white' : 'text-gray-900'}`}>
          {count}
        </p>
      </div>
      <div className={`p-3 rounded-lg ${isActive ? 'bg-white bg-opacity-20' : 'bg-gray-100'}`}>
        <Icon className={`w-6 h-6 ${isActive ? 'text-white' : 'text-gray-600'}`} />
      </div>
    </div>
  </motion.div>
);

const DataTable = ({ data, columns, onEdit, onDelete, onView, loading }) => {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="h-12 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
        <Database className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No data found</h3>
        <p className="text-gray-600">There are no records to display.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {column.label}
                </th>
              ))}
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item, index) => (
              <motion.tr
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="hover:bg-gray-50"
              >
                {columns.map((column) => (
                  <td key={column.key} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {column.render ? column.render(item[column.key], item) : item[column.key]}
                  </td>
                ))}
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => onView(item)}
                      className="text-blue-600 hover:text-blue-900 p-1 rounded"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => onEdit(item)}
                      className="text-green-600 hover:text-green-900 p-1 rounded"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => onDelete(item)}
                      className="text-red-600 hover:text-red-900 p-1 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const DataManagement = () => {
  const [activeTab, setActiveTab] = useState('employeurs');
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState({
    employeurs: 0,
    assures: 0,
    beneficiaires: 0
  });

  const tabs = [
    {
      id: 'employeurs',
      title: 'Employeurs',
      icon: Building,
      color: 'bg-blue-500',
      columns: [
        { key: 'nom_entreprise', label: 'Company Name' },
        { key: 'numero_cnss', label: 'CNSS Number' },
        { key: 'ville', label: 'City' },
        { key: 'secteur_activite', label: 'Sector' },
        { key: 'nombre_employes', label: 'Employees' },
        { 
          key: 'statut', 
          label: 'Status',
          render: (value) => (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              value === 'actif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {value}
            </span>
          )
        }
      ]
    },
    {
      id: 'assures',
      title: 'Assurés',
      icon: Users,
      color: 'bg-green-500',
      columns: [
        { key: 'numero_assure', label: 'Assure Number' },
        { key: 'nom', label: 'Last Name' },
        { key: 'prenom', label: 'First Name' },
        { key: 'cin', label: 'CIN' },
        { key: 'ville', label: 'City' },
        { key: 'employeur_nom', label: 'Employer' },
        { 
          key: 'statut', 
          label: 'Status',
          render: (value) => (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              value === 'actif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {value}
            </span>
          )
        }
      ]
    },
    {
      id: 'beneficiaires',
      title: 'Bénéficiaires',
      icon: UserCheck,
      color: 'bg-purple-500',
      columns: [
        { key: 'numero_beneficiaire', label: 'Beneficiary Number' },
        { key: 'nom', label: 'Last Name' },
        { key: 'prenom', label: 'First Name' },
        { key: 'assure_nom', label: 'Insured Person' },
        { key: 'relation_assure', label: 'Relationship' },
        { 
          key: 'statut', 
          label: 'Status',
          render: (value) => (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              value === 'actif' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {value}
            </span>
          )
        }
      ]
    }
  ];

  const currentTab = tabs.find(tab => tab.id === activeTab);

  useEffect(() => {
    loadData();
    loadStats();
  }, [activeTab]);

  const loadData = async () => {
    setLoading(true);
    try {
      let response;
      switch (activeTab) {
        case 'employeurs':
          response = await dataAPI.getEmployeurs({ search: searchTerm });
          setData(response.employeurs || []);
          break;
        case 'assures':
          response = await dataAPI.getAssures({ search: searchTerm });
          setData(response.assures || []);
          break;
        case 'beneficiaires':
          response = await dataAPI.getBeneficiaires({ search: searchTerm });
          setData(response.beneficiaires || []);
          break;
        default:
          setData([]);
      }
    } catch (error) {
      console.error('Failed to load data:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const [employeursRes, assuresRes, beneficiairesRes] = await Promise.all([
        dataAPI.getEmployeurs().catch(() => ({ employeurs: [], total: 0 })),
        dataAPI.getAssures().catch(() => ({ assures: [], total: 0 })),
        dataAPI.getBeneficiaires().catch(() => ({ beneficiaires: [], total: 0 }))
      ]);

      setStats({
        employeurs: employeursRes.total || employeursRes.employeurs?.length || 0,
        assures: assuresRes.total || assuresRes.assures?.length || 0,
        beneficiaires: beneficiairesRes.total || beneficiairesRes.beneficiaires?.length || 0
      });
    } catch (error) {
      console.error('Failed to load stats:', error);
      setStats({ employeurs: 0, assures: 0, beneficiaires: 0 });
    }
  };

  const handleSearch = () => {
    loadData();
  };

  const handleView = (item) => {
    alert(`View details for: ${JSON.stringify(item, null, 2)}`);
  };

  const handleEdit = (item) => {
    alert(`Edit functionality will be implemented in the next phase for: ${item.id}`);
  };

  const handleDelete = async (item) => {
    if (window.confirm(`Are you sure you want to delete this ${activeTab.slice(0, -1)}?`)) {
      try {
        switch (activeTab) {
          case 'employeurs':
            await dataAPI.deleteEmployeur(item.id);
            break;
          case 'assures':
            await dataAPI.deleteAssure(item.id);
            break;
          case 'beneficiaires':
            await dataAPI.deleteBeneficiaire(item.id);
            break;
        }
        loadData();
        loadStats();
        alert('Record deleted successfully');
      } catch (error) {
        console.error('Failed to delete:', error);
        alert('Failed to delete record: ' + error.message);
      }
    }
  };

  const handleAdd = () => {
    alert(`Add new ${activeTab.slice(0, -1)} functionality will be implemented in the next phase`);
  };

  return (
    <DashboardLayout currentPage="data">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Data Management</h1>
            <p className="text-gray-600 mt-1">Manage employeurs, assurés, and bénéficiaires</p>
          </div>
          <div className="flex space-x-3">
            <button className="flex items-center px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
            <button className="flex items-center px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
              <Upload className="w-4 h-4 mr-2" />
              Import
            </button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleAdd}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add New
            </motion.button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {tabs.map((tab) => (
            <DataCard
              key={tab.id}
              title={tab.title}
              count={stats[tab.id]}
              icon={tab.icon}
              color={tab.color}
              isActive={activeTab === tab.id}
              onClick={() => setActiveTab(tab.id)}
            />
          ))}
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder={`Search ${currentTab?.title.toLowerCase()}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Search
            </button>
            <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </button>
          </div>
        </div>

        {/* Data Table */}
        <DataTable
          data={data}
          columns={currentTab?.columns || []}
          onView={handleView}
          onEdit={handleEdit}
          onDelete={handleDelete}
          loading={loading}
        />
      </div>
    </DashboardLayout>
  );
};

export default DataManagement;
