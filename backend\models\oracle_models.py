from sqlalchemy import Column, Integer, String, Date, DateTime, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Employeur(Base):
    """Employeur table model for Oracle database"""
    __tablename__ = 'employeur'
    
    id = Column(Integer, primary_key=True)
    nom_entreprise = Column(String(255), nullable=False)
    numero_cnss = Column(String(50), unique=True, nullable=False)
    adresse = Column(String(500))
    ville = Column(String(100))
    telephone = Column(String(20))
    email = Column(String(100))
    secteur_activite = Column(String(100))
    date_creation = Column(Date)
    statut = Column(String(50), default='Actif')
    
    def to_dict(self):
        return {
            'id': self.id,
            'nom_entreprise': self.nom_entreprise,
            'numero_cnss': self.numero_cnss,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'email': self.email,
            'secteur_activite': self.secteur_activite,
            'date_creation': self.date_creation.isoformat() if self.date_creation else None,
            'statut': self.statut
        }

class Assure(Base):
    """Assuré table model for Oracle database"""
    __tablename__ = 'assure'
    
    id = Column(Integer, primary_key=True)
    nom = Column(String(100), nullable=False)
    prenom = Column(String(100), nullable=False)
    cin = Column(String(20), unique=True, nullable=False)
    numero_assure = Column(String(50), unique=True, nullable=False)
    date_naissance = Column(Date)
    lieu_naissance = Column(String(100))
    adresse = Column(String(500))
    telephone = Column(String(20))
    email = Column(String(100))
    employeur_id = Column(Integer, ForeignKey('employeur.id'))
    date_affiliation = Column(Date)
    statut = Column(String(50), default='Actif')
    
    # Relationship
    employeur = relationship("Employeur", backref="assures")
    
    def to_dict(self):
        return {
            'id': self.id,
            'nom': self.nom,
            'prenom': self.prenom,
            'cin': self.cin,
            'numero_assure': self.numero_assure,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'lieu_naissance': self.lieu_naissance,
            'adresse': self.adresse,
            'telephone': self.telephone,
            'email': self.email,
            'employeur_id': self.employeur_id,
            'employeur_nom': self.employeur.nom_entreprise if self.employeur else None,
            'date_affiliation': self.date_affiliation.isoformat() if self.date_affiliation else None,
            'statut': self.statut
        }

class Beneficiaire(Base):
    """Bénéficiaire table model for Oracle database"""
    __tablename__ = 'beneficiaire'
    
    id = Column(Integer, primary_key=True)
    nom = Column(String(100), nullable=False)
    prenom = Column(String(100), nullable=False)
    cin = Column(String(20), unique=True)
    date_naissance = Column(Date)
    relation_assure = Column(String(50))  # Époux/Épouse, Enfant, etc.
    assure_id = Column(Integer, ForeignKey('assure.id'))
    numero_beneficiaire = Column(String(50), unique=True)
    statut = Column(String(50), default='Actif')
    date_creation = Column(Date, default=datetime.now().date())
    
    # Relationship
    assure = relationship("Assure", backref="beneficiaires")
    
    def to_dict(self):
        return {
            'id': self.id,
            'nom': self.nom,
            'prenom': self.prenom,
            'cin': self.cin,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'relation_assure': self.relation_assure,
            'assure_id': self.assure_id,
            'assure_nom': f"{self.assure.nom} {self.assure.prenom}" if self.assure else None,
            'numero_beneficiaire': self.numero_beneficiaire,
            'statut': self.statut,
            'date_creation': self.date_creation.isoformat() if self.date_creation else None
        }

class AuditLog(Base):
    """Audit Log table for tracking user actions"""
    __tablename__ = 'audit_log'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, nullable=False)
    username = Column(String(100), nullable=False)
    action = Column(String(100), nullable=False)  # CREATE, UPDATE, DELETE, LOGIN, etc.
    table_name = Column(String(100))  # employeur, assure, beneficiaire, user
    record_id = Column(Integer)  # ID of the affected record
    old_values = Column(Text)  # JSON string of old values
    new_values = Column(Text)  # JSON string of new values
    ip_address = Column(String(45))
    user_agent = Column(String(500))
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.username,
            'action': self.action,
            'table_name': self.table_name,
            'record_id': self.record_id,
            'old_values': self.old_values,
            'new_values': self.new_values,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }
