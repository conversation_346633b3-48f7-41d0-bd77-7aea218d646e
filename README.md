# CNSS Data Management System

A complete and secure web application for public organizations (e.g., CNSS) to manage and share data through APIs over HTTP.

## 🎯 Project Goal

Allow multiple public entities to **publish, access, modify, and remove APIs** related to data stored in Oracle DB. Each organization can manage their own APIs and consume others'. The system is **secure**, **auditable**, and **easy to use**.

## ✅ Features Implemented

- **✅ Secure Authentication**: JWT-based authentication with role-based access control
- **✅ User Management**: Admin interface for managing users and permissions
- **✅ Data Management**: Full CRUD operations for Employeur, Assuré, and Bénéficiaire data
- **✅ Audit Logging**: Comprehensive logging system for tracking all user actions
- **✅ Modern UI**: Responsive React frontend with animations and modern design
- **✅ Role-Based Access**: Different interfaces for Admin and Agent users
- **✅ API-First**: RESTful API design for easy integration
- **✅ Docker Ready**: Complete Docker configuration for deployment

## 🛠 Tech Stack

- **Frontend**: React 18, Tailwind CSS (via CDN), Framer Motion, Lucide React, React Router
- **Backend**: Flask, SQLAlchemy, Flask-JWT-Extended, Flask-CORS, bcrypt
- **Database**: SQLite (development), Oracle 23c (production ready)
- **Deployment**: Docker, Docker Compose, Nginx

## 📋 Database Tables

The system manages three main Oracle tables:

1. **employeur** - Company/employer information (nom_entreprise, numero_cnss, adresse, etc.)
2. **assure** - Insured person data (nom, prenom, cin, numero_assure, etc.)
3. **beneficiaire** - Beneficiary information (nom, prenom, relation_assure, etc.)

## 👥 User Roles

### 🔵 Agent Role
- ✅ Access to Dashboard with real-time data
- ✅ Data Management (view, add, edit, delete records)
- ✅ Settings page for profile management
- ❌ No access to User Management
- ❌ No access to Audit Logs

### 🟣 Admin Role
- ✅ All Agent permissions
- ✅ User Management (create, edit, delete users)
- ✅ Audit Logs (view all system activities)
- ✅ System settings and configuration

## 🚀 Quick Start & Testing

### Prerequisites
- Node.js 18+ and npm
- Python 3.9+
- Both servers must be running simultaneously

### 1. Start the Backend Server
```bash
cd backend
pip install -r requirements.txt
python app.py
```
✅ Backend will be available at `http://localhost:5000`
✅ Sample data is automatically created on first run

### 2. Start the Frontend Server
```bash
npm install  # (if not already done)
npm start
```
✅ Frontend will be available at `http://localhost:3000`
✅ Browser will automatically open to the login page

### 3. Test the Application

#### 🔐 Login Credentials
- **Admin**: `admin` / `admin123`
- **Agent**: Create via admin interface after logging in as admin

#### 🧪 Testing Steps

1. **Test Authentication**:
   - ✅ Should start at login page (not dashboard)
   - ✅ Login with admin credentials
   - ✅ Should redirect to dashboard after successful login

2. **Test Admin Features**:
   - ✅ Dashboard shows real data counts (not static numbers)
   - ✅ Navigate to "User Management" (admin only)
   - ✅ Navigate to "Audit Logs" (admin only)
   - ✅ Navigate to "Data Management"
   - ✅ Navigate to "Settings"

3. **Test Agent Features**:
   - ✅ Create an agent user via admin panel
   - ✅ Logout and login as agent
   - ✅ Should NOT see "User Management" or "Audit Logs" in navigation
   - ✅ Should see Dashboard, Data Management, and Settings only

4. **Test Data Management**:
   - ✅ View Employeurs, Assurés, Bénéficiaires tabs
   - ✅ Search functionality works
   - ✅ Real data from backend (not static)
   - ✅ Delete functionality works with confirmation

5. **Test Navigation**:
   - ✅ All navigation links work properly
   - ✅ Page titles update correctly
   - ✅ Sidebar navigation works on mobile
   - ✅ Logout redirects to login page

## 📡 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - Register new user (admin only)
- `GET /api/auth/me` - Get current user info

### User Management (Admin Only)
- `GET /api/users` - Get all users
- `PUT /api/users/{id}` - Update user
- `DELETE /api/users/{id}` - Delete user

### Data Management
- `GET /api/employeurs` - Get employers (with pagination & search)
- `POST /api/employeurs` - Create employer
- `GET /api/assures` - Get insured persons (with pagination & search)
- `POST /api/assures` - Create insured person
- `GET /api/beneficiaires` - Get beneficiaries (with pagination & search)
- `POST /api/beneficiaires` - Create beneficiary

### Audit Logs (Admin Only)
- `GET /api/audit-logs` - Get audit logs with pagination

### Health Check
- `GET /api/health` - System health check

## 🐳 Docker Deployment

### Development
```bash
docker-compose -f docker-compose.dev.yml up --build
```

### Production
```bash
docker-compose up --build -d
```

## 🔧 Configuration

### Environment Variables (backend/.env)
```env
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=jwt-secret-string-change-this-in-production
DATABASE_URL=sqlite:///cnss_users.db
ORACLE_USER=your_oracle_user
ORACLE_PASSWORD=your_oracle_password
ORACLE_DSN=localhost:1521/XE
```

## 🔒 Security Features

- ✅ JWT token-based authentication
- ✅ Role-based access control (Admin/Agent)
- ✅ Password hashing with bcrypt
- ✅ CORS protection
- ✅ Input validation and sanitization
- ✅ Audit logging for all actions
- ✅ Token expiration handling

## 📊 Current Status

### ✅ Completed Features
- [x] Login page with proper authentication
- [x] Role-based navigation (different for Admin/Agent)
- [x] Dashboard with real-time data (not static numbers)
- [x] Data Management with CRUD operations
- [x] User Management (Admin only)
- [x] Audit Logs (Admin only)
- [x] Settings page
- [x] Responsive design with animations
- [x] Docker configuration
- [x] API documentation

### 🔄 Ready for Oracle Integration
- [x] Database models defined for Oracle tables
- [x] API endpoints ready for Oracle connection
- [x] Sample data structure matches Oracle schema
- [x] Easy migration from SQLite to Oracle

## 🚀 Next Steps

1. **Oracle Database Integration**:
   - Replace SQLite with Oracle 23c connection
   - Update connection string in environment variables
   - Test with real Oracle data

2. **Production Deployment**:
   - Configure SSL certificates
   - Set up production environment variables
   - Deploy using Docker Compose

3. **Additional Features**:
   - Advanced search and filtering
   - Data export/import functionality
   - Email notifications
   - API rate limiting

## 📞 Support

The application is fully functional and ready for testing. All authentication issues have been resolved:
- ✅ Proper login flow (starts at login page)
- ✅ Role-based navigation working
- ✅ Real data integration (no static numbers)
- ✅ All pages accessible based on user role

For any issues, check the browser console and terminal outputs for detailed error messages.
