from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='agent')  # 'agent' or 'admin'
    organization = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'organization': self.organization,
            'created_at': self.created_at.isoformat(),
            'is_active': self.is_active
        }

class AuditLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)
    table_name = db.Column(db.String(50))
    record_id = db.Column(db.String(50))
    details = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(45))

    user = db.relationship('User', backref=db.backref('audit_logs', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.user.username if self.user else None,
            'action': self.action,
            'table_name': self.table_name,
            'record_id': self.record_id,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'ip_address': self.ip_address
        }

# Oracle database models for business data
class Employeur(db.Model):
    __tablename__ = 'employeur'
    id = db.Column(db.Integer, primary_key=True)
    nom_entreprise = db.Column(db.String(200), nullable=False)
    numero_cnss = db.Column(db.String(50), unique=True, nullable=False)
    adresse = db.Column(db.String(500))
    ville = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    secteur_activite = db.Column(db.String(100))
    nombre_employes = db.Column(db.Integer)
    date_affiliation = db.Column(db.Date)
    statut = db.Column(db.String(20), default='actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'nom_entreprise': self.nom_entreprise,
            'numero_cnss': self.numero_cnss,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'email': self.email,
            'secteur_activite': self.secteur_activite,
            'nombre_employes': self.nombre_employes,
            'date_affiliation': self.date_affiliation.isoformat() if self.date_affiliation else None,
            'statut': self.statut,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Assure(db.Model):
    __tablename__ = 'assure'
    id = db.Column(db.Integer, primary_key=True)
    numero_assure = db.Column(db.String(50), unique=True, nullable=False)
    cin = db.Column(db.String(20), unique=True, nullable=False)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    date_naissance = db.Column(db.Date)
    lieu_naissance = db.Column(db.String(100))
    sexe = db.Column(db.String(1))
    adresse = db.Column(db.String(500))
    ville = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    employeur_id = db.Column(db.Integer, db.ForeignKey('employeur.id'))
    date_affiliation = db.Column(db.Date)
    statut = db.Column(db.String(20), default='actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    employeur = db.relationship('Employeur', backref=db.backref('assures', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'numero_assure': self.numero_assure,
            'cin': self.cin,
            'nom': self.nom,
            'prenom': self.prenom,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'lieu_naissance': self.lieu_naissance,
            'sexe': self.sexe,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'email': self.email,
            'employeur_id': self.employeur_id,
            'employeur_nom': self.employeur.nom_entreprise if self.employeur else None,
            'date_affiliation': self.date_affiliation.isoformat() if self.date_affiliation else None,
            'statut': self.statut,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class Beneficiaire(db.Model):
    __tablename__ = 'beneficiaire'
    id = db.Column(db.Integer, primary_key=True)
    numero_beneficiaire = db.Column(db.String(50), unique=True, nullable=False)
    assure_id = db.Column(db.Integer, db.ForeignKey('assure.id'), nullable=False)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    date_naissance = db.Column(db.Date)
    sexe = db.Column(db.String(1))
    relation_assure = db.Column(db.String(50))  # conjoint, enfant, parent, etc.
    adresse = db.Column(db.String(500))
    ville = db.Column(db.String(100))
    telephone = db.Column(db.String(20))
    statut = db.Column(db.String(20), default='actif')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    assure = db.relationship('Assure', backref=db.backref('beneficiaires', lazy=True))

    def to_dict(self):
        return {
            'id': self.id,
            'numero_beneficiaire': self.numero_beneficiaire,
            'assure_id': self.assure_id,
            'assure_nom': f"{self.assure.nom} {self.assure.prenom}" if self.assure else None,
            'nom': self.nom,
            'prenom': self.prenom,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'sexe': self.sexe,
            'relation_assure': self.relation_assure,
            'adresse': self.adresse,
            'ville': self.ville,
            'telephone': self.telephone,
            'statut': self.statut,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
